import os
import json
import logging
import traceback
import time
import socket
import redis
from http import HTT<PERSON>tatus
from http.server import HTTPServer, BaseHTTPRequestHandler
from http.server import Threading<PERSON>TPServer
from utils.helpers import REQUEST_ID
from config.settings import Config
from celery.schedules import crontab
from celery import Celery
from celery.signals import after_setup_logger, worker_process_init
from retro_runner import retro_runner
from threading import Thread, Lock
from db.db_ops import DbOps, RetroStatus
from redis.sentinel import Sentinel
import copy
from datetime import datetime, timezone
import uuid


app_config = Config()
# Logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Thread coordination
thread_lock = Lock()
db_ops = DbOps()

# Celery configuration
if app_config.REDIS_SENTINEL_URL:
    app_config.REDIS_URL = app_config.REDIS_SENTINEL_URL

app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
app.conf.broker_transport_options = {'visibility_timeout': 300}  # 5 minutes.
if app_config.REDIS_SENTINEL_URL:
    app.conf.broker_transport_options = {
        "master_name": app_config.REDIS_SENTINEL_SERVICE_NAME,
    }
app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    worker_max_tasks_per_child=1,
    task_default_queue=app_config.RETRO_INPUT_REDIS_QUEUE,  # Add this line
    task_routes={
        'process_retro_task': {'queue': app_config.RETRO_INPUT_REDIS_QUEUE},
    }
)

@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    os.makedirs('logs', exist_ok=True)
    fh = logging.FileHandler('logs/tasks.log')
    fh.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(fh)




# Redis utility to push result
class RedisClient:
    def __init__(self, redis_url, is_sentinel=False):
        if is_sentinel:
            self.sentinel = Sentinel(app_config.REDIS_SENTINEL_URL, socket_timeout=5)
            self.redis = self.sentinel.master_for(app_config.REDIS_SENTINEL_SERVICE_NAME)
        else:
            self.redis = redis.Redis.from_url(redis_url, decode_responses=True)
        self.redis.ping()

    def insert_into_output_queue(self, data):
        self.redis.rpush(app_config.RETRO_OUTPUT_REDIS_QUEUE, json.dumps(data))

    def get_queue_length(self, queue_name):
        return self.redis.llen(queue_name)

# Health check
class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            try:
                client = RedisClient(app_config.REDIS_URL, True if app_config.REDIS_SENTINEL_URL else False)
                data = {
                    "status": "healthy",
                    "output_queue_length": client.get_queue_length(app_config.RETRO_OUTPUT_REDIS_QUEUE),
                    "timestamp": time.time()
                }
            except Exception as e:
                data = {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": time.time()
                }

            try:
                self.send_response(HTTPStatus.OK)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(data).encode())
            except (BrokenPipeError, ConnectionResetError, socket.error) as e:
                logger.warning(f"[HEALTH] Client disconnected before response could be sent: {e}")

    def log_message(self, format, *args):
        return



def handle_error(e, request_id, target_smiles, molecule_name, self_task, start_time, db_ops : DbOps):
    error_msg = str(e)
    logger.error(f"[ERROR] {request_id}: {error_msg}")
    logger.error(traceback.format_exc())
    
    if self_task.request.retries < self_task.max_retries:
        raise self_task.retry(countdown=60 * (2 ** self_task.request.retries))
    
    db_ops.insert_log(
        request_id=request_id,
        input_type="SMILES",
        input_value=target_smiles,
        status=RetroStatus.FAILED,
        error_message=error_msg
    )
    
    app.send_task(
        'retro_result_handler',
        args=[{
            "request_id": request_id,
            "molecule_name": molecule_name,
            "status": "FAILED",
            "error_message": error_msg,
            "failed_at": time.time(),
            "task_id": self_task.request.id,
            "retries": self_task.request.retries,
            "processing_time": time.time() - start_time
        }],
        queue=app_config.RETRO_OUTPUT_REDIS_QUEUE
    )

def process_single_iteration(itr, request_id, target_smiles, molecule_name, self_task, start_time, db_ops : DbOps, thread_idx=0, results_dict=None,first_value=0, last_value=0):
    # Create unique request ID for this thread iteration
    thread_request_id = f"{request_id}_{thread_idx}"

    try:
        # Get thread-specific parameters
        max_depth = int(itr.get('MAX_DEPTH', 5))
        beam_width = int(itr.get('BEAM_WIDTH', 3))
        delay = int(itr.get('DELAY', 0))

        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Starting iteration with max_depth={max_depth} and beam_width={beam_width} for request_id {thread_request_id}")

        # Use thread-specific request ID for database operations
        if str(itr.get('DELAY')) == str(first_value):
            db_ops.insert_log(
                request_id=request_id,
                input_type="SMILES",
                input_value=target_smiles,
                status=RetroStatus.RUNNING,
            )

        checkpoint_frequency = int(str((max_depth + 1) * 100))
        kwargs = {
            "max_depth": max_depth,
            "beam_width": beam_width,
            'delay': delay,
            "checkpoint_frequency": checkpoint_frequency,
            "request_id": request_id,
            "target_smiles": target_smiles,
            'molecule_name': molecule_name
        }

        if db_ops.check_config_is_processed(request_id, kwargs):
            logger.info(f"[THREAD-{thread_idx}] Config already processed for request_id {thread_request_id} with config: {kwargs}")
            return True

        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Processing task {thread_request_id} with SMILES: {target_smiles}")

        # Run the retro synthesis
        total_routes, message = retro_runner(target_smiles, request_id=thread_request_id, kwargs=kwargs)
        processing_time = time.time() - start_time

        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Completed task {thread_request_id} in {processing_time:.2f}s with {total_routes} routes")

        # Update database with completion status
        if str(itr.get('DELAY'))==str(last_value):
            db_ops.insert_log(
                request_id=request_id,
                input_type="SMILES",
                input_value=target_smiles,
                status=RetroStatus.COMPLETED,
            )

        # Send result to output queue
        result_data = {
            "request_id": request_id,
            "original_request_id": request_id,
            "molecule_name": molecule_name,
            "status": "SUCCESS" if message == '' else "UNSOLVABLE",
            "message": message,
            "total_routes": total_routes,
            "processed_at": time.time(),
            "task_id": self_task.request.id,
            "processing_time": processing_time,
            "thread_index": thread_idx,
            "max_depth": itr.get('MAX_DEPTH'),
            "beam_width": itr.get('BEAM_WIDTH')
        }

        if str(itr.get('DELAY'))==str(last_value):
            app.send_task(
                'retro_result_handler',
                args=[result_data],
                queue=app_config.RETRO_OUTPUT_REDIS_QUEUE
            )

        # Store result in shared dictionary if provided
        if results_dict is not None:
            with thread_lock:
                results_dict[thread_idx] = {
                    "success": True,
                    "routes": total_routes,
                    "processing_time": processing_time,
                    "config": itr
                }

        with thread_lock:
            logger.info(f"[THREAD-{thread_idx}] Successfully completed processing for {thread_request_id}")

        return True

    except Exception as e:
        with thread_lock:
            logger.error(f"[THREAD-{thread_idx}] Error in thread {thread_idx} for request {thread_request_id}: {str(e)}")
            logger.error(traceback.format_exc())

        # Store error result in shared dictionary if provided
        if results_dict is not None:
            with thread_lock:
                results_dict[thread_idx] = {
                    "success": False,
                    "error": str(e),
                    "processing_time": time.time() - start_time,
                    "config": itr
                }

        # Handle error for this specific thread
        handle_error(e, thread_request_id, target_smiles, molecule_name, self_task, start_time, db_ops)
        return False
    

def is_data_preprossed(target_smiles, request_id):
    old_request_id = db_ops.fetch_latest_retro_request_id_by_smiles(target_smiles)
    if not old_request_id:
        logger.warning(f"No previous request found for SMILES: {target_smiles}")
        return False

    old_retro_data = db_ops.fetch_retro_data_by_request_id_and_target_smiles(old_request_id, target_smiles)
    if not old_retro_data:
        logger.info(f"No existing retro data found for SMILES: {target_smiles}")
        return False

    logger.info(f"[START] New Request Id {request_id} and Found total {len(old_retro_data)} for old retro data for request {old_request_id}")

    now = datetime.now(timezone.utc)
    new_docs = []
    old_records_found = False
    for retro_doc in old_retro_data:
        new_doc = copy.deepcopy(retro_doc)
        new_doc.update({
            "request_id": request_id,
            "created_at": now,
            "updated_at": now,
            "_id": str(uuid.uuid4())
        })
        new_docs.append(new_doc)

    try:
        db_ops.db['retro_data'].insert_many(new_docs)
        logger.info(f"[START] Inserted {len(new_docs)} retro data for request {request_id}")
        old_records_found = True
    except Exception as e:
        logger.error(f"Failed to insert retro data: {e}\n{traceback.format_exc()}")
        return False
    
    if old_records_found:
        logger.info(f"[UPDATED] Existing retro data updated for request {request_id} and old request {old_request_id}" )
        db_ops.insert_log(
            request_id=request_id,
            input_type="SMILES",
            input_value=target_smiles,
            status=RetroStatus.COMPLETED,
        )
        return True
    return False

@app.task(bind=True, max_retries=3, name='process_retro_task')
def process_retro_task(self, payload):
    start_time = time.time()
    redis_client = RedisClient(app_config.REDIS_URL, True if app_config.REDIS_SENTINEL_URL else False)
    
    required_fields = {"request_id", "target_smiles"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        error_msg = f"Missing required fields: {missing}"
        logger.error(error_msg)
        if "request_id" in payload:
            redis_client.insert_into_output_queue({
                "request_id": payload.get("request_id"),
                "molecule_name": payload.get("molecule_name", "unknown"),
                "status": "FAILED",
                "error_message": error_msg,
                "task_id": self.request.id,
                "processing_time": time.time() - start_time
            })
        return False

    request_id = str(payload["request_id"])
    REQUEST_ID.set(request_id)  # Set the request_id in context variable
    
    molecule_name = payload["molecule_name"]
    target_smiles = payload["target_smiles"]

    for env_key, payload_key in {
        'SYNTHESIS_SCORE_THRESHOLD': 'synthesis_score_threshold',
        'MIN_FORWARD_PROB': 'min_forward_prob',
        'MIN_CERTAINITY_SCORE': 'min_certainity_score',
        'HEAVY_METAL_THRESHOLD': 'heavy_metal_threshold',
        'PRUNING_FACTOR': 'pruning_factor',
        'BEAM_BASED_PRUNING': 'beam_based_pruning',
        'MAX_ROUTES': 'max_routes'
    }.items():
        if payload_key in payload:
            os.environ[env_key] = str(payload[payload_key])
    

    
    start_time = time.time()
    logger.info(f"[START] Processing task {request_id} with SMILES: {target_smiles}")

    if is_data_preprossed(target_smiles, request_id):
        logger.info("[END] Data Processed")
        result_data = {
            "request_id": request_id,
            "original_request_id": request_id,
            "molecule_name": molecule_name,
            "status": "SUCCESS",
            "total_routes": 0,
            "processed_at": time.time(),
            "processing_time": time.time(),
        }

        app.send_task(
            'retro_result_handler',
            args=[result_data],
            queue=app_config.RETRO_OUTPUT_REDIS_QUEUE
        )
        return True
    

    # Parse parallel configurations
    app_config.PARALLEL_CONFIGS = json.loads(app_config.PARALLEL_CONFIGS) if isinstance(app_config.PARALLEL_CONFIGS, str) else app_config.PARALLEL_CONFIGS

    threads = []
    thread_results = {}

    logger.info(f"[PARALLEL] Starting {len(app_config.PARALLEL_CONFIGS)} parallel threads for request {request_id}")

    last_value = 0
    for idx, itr in enumerate(app_config.PARALLEL_CONFIGS):
        last_value = itr.get('DELAY')
    first_value = 0
    for idx, itr in enumerate(app_config.PARALLEL_CONFIGS):
        logger.info(f"[SEQUENTIAL] Processing iteration {idx} with config: {itr}")
        if not first_value:
            first_value = itr.get('DELAY')
        
        # Direct function call instead of threading
        result = process_single_iteration(
            itr, request_id, target_smiles, molecule_name, self,
            start_time, db_ops, idx, thread_results,
            first_value, last_value
        )
        
        # Store result as if it came from a thread
        thread_results[idx] = result

    # Summary logging
    completed_threads = len(app_config.PARALLEL_CONFIGS)
    successful_threads = 0
    failed_threads = 0

    logger.info(f"[END] All {completed_threads} sequential iterations completed for task {request_id}")
    logger.info(f"[SUMMARY] Success: {successful_threads}, Failed: {failed_threads}, Total: {completed_threads}")

    return True



def main():
    logger.info("[MAIN] Celery worker will pull directly from Redis broker.")


if __name__ == "__main__":
    main()
