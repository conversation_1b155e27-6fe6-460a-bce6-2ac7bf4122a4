import sys
import os
import pickle
import time
import redis
from core.tree.tree_nodes import *
from core.tree.scoring import *
from core.tree.tree_search import *
from core.validators.disconnection_validator import *
from core.validators.terminal_checker import *
from treeSearchEngine import *
from config.settings import Config
from utils.checkpointing import *

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python query_checkpoint.py list                    # List all checkpoints")
        print("  python query_checkpoint.py info <checkpoint_id>    # Get checkpoint info")
        print("  python query_checkpoint.py routes <checkpoint_id> [max_routes]  # Get best routes")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        checkpoints = list_available_checkpoints()
        if not checkpoints:
            print("No checkpoints found.")
            return
        
        print(f"Found {len(checkpoints)} checkpoints:")
        for checkpoint_id in checkpoints:
            info = get_checkpoint_info(checkpoint_id)
            if info:
                print(f"  {checkpoint_id} - {info['readable_time']} - Target: {info['target_smiles']}")
            else:
                print(f"  {checkpoint_id} - (error reading checkpoint)")
    
    elif command == "info":
        if len(sys.argv) < 3:
            print("Please provide checkpoint ID")
            return
        
        checkpoint_id = sys.argv[2]
        info = get_checkpoint_info(checkpoint_id)
        
        if info:
            print(f"Checkpoint ID: {info['id']}")
            print(f"Created: {info['readable_time']}")
            print(f"Target molecule: {info['target_smiles']}")
        else:
            print(f"Checkpoint {checkpoint_id} not found or corrupted")
    
    elif command == "routes":
        if len(sys.argv) < 3:
            print("Please provide checkpoint ID")
            return
        
        checkpoint_id = sys.argv[2]
        max_routes = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        
        # Create a minimal engine just for checkpoint querying
        # Note: We don't need the full single_step_api for this
        synthesis_score_api=SCScoreAPI()
        settings = Config()        
        engine = TreeSearchEngine(single_step_api=None)
        
        try:
            best_routes = engine.get_best_routes_from_checkpoint(checkpoint_id, max_routes)
            
            if not best_routes:
                print(f"No complete routes found in checkpoint {checkpoint_id}")
                return
            
            print(f"Top {len(best_routes)} routes from checkpoint {checkpoint_id}:")
            print("=" * 60)
            
            for i, route in enumerate(best_routes):
                print(f"\nRoute {i+1} ({len(route)} steps):")
                for j, reaction in enumerate(route):
                    rxn_string = reaction.reaction_data.get('rxn_string', 'N/A')
                    score = reaction.reaction_data.get('Score', 'N/A')
                    print(f"  Step {j+1}: {rxn_string} (Score: {score})")
                
                # Calculate total route score
                total_score = sum(
                    reactant.synthesis_score or 0 
                    for reaction in route 
                    for reactant in reaction.get_reactant_nodes()
                )
                print(f"  Total Route Score: {total_score:.3f}")
        
        except Exception as e:
            print(f"Error retrieving routes from checkpoint {checkpoint_id}: {e}")
    
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()

'''
# List all available checkpoints
python query_checkpoint.py list

# Output:
# Found 3 checkpoints:
#   checkpoint_1633024800 - Wed Sep 01 10:00:00 2021 - Target: C1CCOC1
#   checkpoint_1633025400 - Wed Sep 01 10:10:00 2021 - Target: C1CCOC1
#   checkpoint_1633026000 - Wed Sep 01 10:20:00 2021 - Target: C1CCOC1

# Get info about a specific checkpoint
python query_checkpoint.py info checkpoint_1633025400

# Get best routes from latest checkpoint
python query_checkpoint.py routes checkpoint_1633026000 5

# Output:
# Top 3 routes from checkpoint_1633026000:
# ============================================================
# 
# Route 1 (2 steps):
#   Step 1: OCCCCO>CC(C)=O.CCCCCCCCCCCCCCCCC[N+](C)(C)C>C1CCOC1 (Score: 0.956)
#   Step 2: ...
#   Total Route Score: 2.145
# '''