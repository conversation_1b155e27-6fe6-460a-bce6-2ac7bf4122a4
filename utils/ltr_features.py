import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import rdFMCS
from api.synthesis_score_api import *
from core.validators.terminal_checker import *


def parse_reactants(retro_smiles: str) -> List[str]:
    """
    Parse reactants from the Retro field.
    Assumes reactants are separated by '.' (common in SMILES notation).
    You may need to adjust the delimiter based on your data format.
    """
    if pd.isna(retro_smiles) or retro_smiles == '':
        return []
    
    # Split by '.' which is common for separating molecules in SMILES
    # You might need to adjust this delimiter based on your data
    reactants = [smiles.strip() for smiles in retro_smiles.split('.') if smiles.strip()]
    return reactants

def mcs_containment_similarity(target, precursor):
    """
    Calculate how much of the target molecule is contained in the precursor
    Returns value between 0 and 1
    """
    # None check
    if target is None or precursor is None or target == '' or precursor == '':
        return -1
        
    try:
        # Convert to mol
        target_mol = Chem.MolFromSmiles(target)
        precursor_mol = Chem.MolFromSmiles(precursor)

        if target_mol is None or precursor_mol is None:
            return -1
        
        # Find MCS between target and precursor
        mcs = rdFMCS.FindMCS([target_mol, precursor_mol], timeout=1)
        common_atoms = mcs.numAtoms
        
        # Total atoms in target
        target_atoms = target_mol.GetNumAtoms()
        
        # Similarity as fraction of target contained in precursor
        similarity = common_atoms / target_atoms if target_atoms > 0 else 0
        
        return similarity
    except Exception as e:
        print(f"MCS calculation error for {target} vs {precursor}: {e}")
        return -1

def enhance_dataframe_with_scores(df: pd.DataFrame,
                                          terminal_threshold: float = 1.5) -> pd.DataFrame:
    """
    Add synthesis score columns to the dataframe (optimized version).
    Args:
        df: Input dataframe
        terminal_threshold: Threshold for terminal condition
    Returns:
        Enhanced dataframe with additional columns
    """
    
    # Create a copy to avoid modifying the original
    enhanced_df = df.copy()
    
    # Initialize new columns with vectorized operations
    enhanced_df['num_reactants'] = 0
    enhanced_df['total_synthesis_score'] = np.nan
    enhanced_df['max_synthesis_score'] = np.nan
    enhanced_df['min_synthesis_score'] = np.nan
    enhanced_df['has_terminal_reactant'] = False
    enhanced_df['trivial_reaction'] = (enhanced_df['Target'] == enhanced_df['Retro'])
    enhanced_df['Max_Substruct_Sim'] = -1.0
    enhanced_df['Avg_Substruct_Sim'] = -1.0
    enhanced_df['Min_Substruct_Sim'] = -1.0
    
    # Create API instances once (outside the loop)
    synthesis_score_api = SCScoreAPI()
    terminal_checker = SynthesisScoreTerminalChecker(terminal_threshold, synthesis_score_api)
    
    def process_row(row):
        """Process a single row and return computed values as a Series"""
        try:
            target = row['Target']
            reactants = parse_reactants(row['Retro'])
            num_reactants = len(reactants)
            
            # Initialize return values
            result = {
                'num_reactants': num_reactants,
                'total_synthesis_score': np.nan,
                'max_synthesis_score': np.nan,
                'min_synthesis_score': np.nan,
                'has_terminal_reactant': False,
                'Max_Substruct_Sim': -1.0,
                'Avg_Substruct_Sim': -1.0,
                'Min_Substruct_Sim': -1.0
            }
            
            if num_reactants == 0:
                return pd.Series(result)
            
            # Process all reactants in batch-like manner
            synthesis_scores = []
            similarities = []
            has_terminal = False
            
            for reactant_smiles in reactants:
                try:
                    # Get synthesis score
                    score = synthesis_score_api.get_synthesis_score(reactant_smiles)
                    synthesis_scores.append(score)
                    
                    # Check if terminal
                    if terminal_checker.is_terminal_smiles(score):
                        has_terminal = True
                    
                    # Calculate similarity
                    sim = mcs_containment_similarity(target, reactant_smiles)
                    # sim = mcs_containment_similarity_fast(target, reactant_smiles)
                    if sim >= 0:  # Valid similarity
                        similarities.append(sim)
                        
                except Exception as e:
                    print(f"Error processing reactant {reactant_smiles}: {e}")
                    continue
            
            # Calculate aggregate scores
            if synthesis_scores:
                result['total_synthesis_score'] = sum(synthesis_scores)
                result['max_synthesis_score'] = max(synthesis_scores)
                result['min_synthesis_score'] = min(synthesis_scores)
                result['has_terminal_reactant'] = has_terminal
            
            if similarities:
                result['Max_Substruct_Sim'] = max(similarities)
                result['Avg_Substruct_Sim'] = np.mean(similarities)
                result['Min_Substruct_Sim'] = min(similarities)
            
            return pd.Series(result)
            
        except Exception as e:
            print(f"Error processing row: {e}")
            # Return default values on error
            return pd.Series({
                'num_reactants': 0,
                'total_synthesis_score': np.nan,
                'max_synthesis_score': np.nan,
                'min_synthesis_score': np.nan,
                'has_terminal_reactant': False,
                'Max_Substruct_Sim': -1.0,
                'Avg_Substruct_Sim': -1.0,
                'Min_Substruct_Sim': -1.0
            })
    
    # Apply the processing function and update columns in batch
    computed_values = enhanced_df.apply(process_row, axis=1)
    
    # Update the dataframe with computed values (vectorized assignment)
    for col in computed_values.columns:
        enhanced_df[col] = computed_values[col]
    
    return enhanced_df

def create_derived_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create derived features from the existing ones.
    Args:
        df: Input dataframe
    Returns:
        Dataframe with additional columns
    """
    # Create a copy to avoid modifying the original
    df_features = df.copy()
    # Add transformed confidence features
    df_features['log_retro_conf'] = np.log1p(df['Retro_Conf'])
    df_features['sqrt_retro_conf'] = np.sqrt(df['Retro_Conf'])
    
    # Enhanced confidence features (without percentile)
    df_features['conf_rank_squared'] = df['Retro_Conf'] ** 2
    
    # MCS features if available
    if 'Max_Substruct_Sim' in df.columns:
        df_features['Max_Substruct_Sim_clean'] = df['Max_Substruct_Sim'].replace(-1, 0)
        df_features['Avg_Substruct_Sim_clean'] = df['Avg_Substruct_Sim'].replace(-1, 0)
        df_features['Min_Substruct_Sim_clean'] = df['Min_Substruct_Sim'].replace(-1, 0)
        
        # MCS interaction features (without percentile)
        df_features['mcs_conf_product'] = df_features['Max_Substruct_Sim_clean'] * df['Retro_Conf']
        df_features['high_mcs_indicator'] = (df_features['Max_Substruct_Sim_clean'] > 0.5).astype(int)
        df_features['mcs_range'] = (df_features['Max_Substruct_Sim_clean'] - df_features['Min_Substruct_Sim_clean'])
        df_features['mcs_x_conf_rank'] = df_features['Max_Substruct_Sim_clean'] * df['Retro_Conf']
    
    return df_features