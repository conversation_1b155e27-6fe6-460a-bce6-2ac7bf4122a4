import pandas as pd

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from core.single_step_retro.disconnections import *
from infra.db.disconnections_fileStorage import *
from api.reaction_classification_api import *
from api.trasformers_api import *
def canonicalize_smiles(smiles: str) -> str:
    '''
    Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
    Also neutralizes any charge of the molecules.
    
    Args:
        smiles (str): SMILES string of the molecule(s).
    
    Returns:
        str: Canonicalized SMILES string of the molecule(s).
    '''
    returned = []
    any_error = False
    for molecule in smiles.split('.'):
        molecule = neutralize_smi(molecule)
        mol = Chem.MolFromSmiles(molecule)
        if mol is not None:
            returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
        else: 
            any_error = True
    if not any_error:
        return '.'.join(returned)
    else:
        return ''
    
def neutralize_smi(smiles: str) -> str:        # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
    if '-' in smiles or '+' in smiles:
        try:
            mol = Chem.MolFromSmiles(smiles)
            pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
            at_matches = mol.GetSubstructMatches(pattern)
            at_matches_list = [y[0] for y in at_matches]
            if len(at_matches_list) > 0:
                for at_idx in at_matches_list:
                    atom = mol.GetAtomWithIdx(at_idx)
                    chg = atom.GetFormalCharge()
                    hcount = atom.GetTotalNumHs()
                    atom.SetFormalCharge(0)
                    atom.SetNumExplicitHs(hcount - chg)
                    atom.UpdatePropertyCache()
            return Chem.MolToSmiles(mol)
        except:
            return smiles
    else:
        return smiles

def check_reactant_in_retro(df, reactant, column_name='Retro'):
    """
    Check if a given reactant is present in the Retro column of a dataframe.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        The dataframe containing the Retro column
    reactant : str
        The reactant SMILES string to search for
    column_name : str, default 'Retro'
        The name of the column containing reactants separated by '.'
    
    Returns:
    --------
    pandas.Series
        Boolean series indicating whether the reactant is present in each row
    """
    return df[column_name].str.contains(f'\\b{reactant}\\b', regex=True, na=False)

def check_reactant_exact_match(df, reactant, column_name='Retro'):
    """
    Check if a given reactant is present in the Retro column using exact matching.
    This splits on '.' and checks for exact matches in the list of reactants.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        The dataframe containing the Retro column
    reactant : str
        The reactant SMILES string to search for
    column_name : str, default 'Retro'
        The name of the column containing reactants separated by '.'
    
    Returns:
    --------
    pandas.Series
        Boolean series indicating whether the reactant is present in each row
    """
    return df[column_name].apply(
        lambda x: reactant in x.split('.') if pd.notna(x) else False
    )

#Example usage:
#Check if 'Cc1cc(C(=O)O)cc(C(=O)O)c1N' is present
# result = check_reactant_exact_match(df, 'Cc1cc(C(=O)O)cc(C(=O)O)c1N')

# Or check multiple reactants:
# reactants_to_check = ['Cc1cc(C(=O)O)cc(C(=O)O)c1N', 'Cc1cc(CO)cc(C(=O)O)c1N']
# for reactant in reactants_to_check:
#     mask = check_reactant_exact_match(df, reactant)
#     print(f"Reactant '{reactant}' found in {mask.sum()} rows")


if __name__=="__main__":
    # python retro_runner.py --target-smiles "NC(=O)c1ccncc1C(F)(F)F" --name "3-(trifluoromethyl)pyridine-4-carboxamide" 
    # python retro_runner.py --target-smiles "Nc1ccc(OC(F)(F)F)cc1" --name "4-(trifluoromethoxy)aniline"
    # python retro_runner.py --target-smiles "[O-][N+](=O)c1cc(Cl)c(F)cc1Cl" --name "2,5-Dichloro-4-fluoro-1-nitrobenzene"
    # python retro_runner.py --target-smiles "Cc1ccc(Cl)c(C=O)c1" --name "2-Chloro-5-methylbenzaldehyde"
    # python retro_runner.py --target-smiles "CS(=O)(=O)CC(F)F" --name "2,2-Difluoroethyl methyl sulfone"    
    #load_df:
    precursors={
        "molecule_1":
        {
            "Target": "NC(=O)c1ccncc1C(F)(F)F",
            "Precursor_1":"O=C(Cl)c1ccncc1C(F)(F)F"
        },
        "molecule_2":
        {
            "Target": "Nc1ccc(OC(F)(F)F)cc1",
            "Precursor_1":"Nc1ccc(OC(F)(F)F)c(N)c1"
        },  
        "molecule_3":
        {
            "Target": "[O-][N+](=O)c1cc(Cl)c(F)cc1Cl",
            "Precursor_1":"c1ccc(Cl)c(F)c1Cl"
        },  
        "molecule_4":
        {
            "Target": "Cc1ccc(Cl)c(C=O)c1",
            "Precursor_1":"Cc1ccc(Cl)cc1Br"
        },   
        "molecule_5":
        {
            "Target": "CS(=O)(=O)CC(F)F",
            "Precursor_1":"FC(F)CS(=O)(=O)Cl"
        }                                        
    }  
    precursors={
        "molecule_2":
        {
            "Target": "Nc1ccc(OC(F)(F)F)cc1",
            "Precursor_1":"Nc1ccc(OC(F)(F)F)c(N)c1"
        },  
        "molecule_3":
        {
            "Target": "[O-][N+](=O)c1cc(Cl)c(F)cc1Cl",
            "Precursor_1":"c1ccc(Cl)c(F)c1Cl"
        },
        "molecule_5":
        {
            "Target": "CS(=O)(=O)CC(F)F",
            "Precursor_1":"FC(F)CS(=O)(=O)Cl"
        }                                                 
    }         
    for key in precursors.keys():
        print(key)
        target_smile = precursors[key]["Target"]
        precusor_1_smile = precursors[key]["Precursor_1"]

        #storage = DisconnectionsStorage('opt/disconnections') 
        storage = DisconnectionsStorage('opt/disconnections_relaxed') 
        target_predictions = storage.load_disconnections(canonicalize_smiles(target_smile))         
        print(f"Loaded {len(target_predictions)} records from fileStorage")  
        if len(target_predictions) >0:
            print(target_predictions.shape)
            #print(target_predictions.iloc[0])
            relaxed = 1
            if relaxed == 0:    
                target_predictions= target_predictions[['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']]
            else:
                target_predictions= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']]

            #target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
            result = check_reactant_exact_match(target_predictions, canonicalize_smiles(precusor_1_smile))
            print(f"Loaded {len(target_predictions[result])} match records from fileStorage")
            #print(len(target_predictions[result]))
            #print(target_predictions[result].iloc[0])
        
            # if len(target_predictions[result]) > 0:
            #     #post filtering misses  
            #     #remove invalid discconections by roundtrip logid
            #     # Keep predictions where T3 predicts the correct target, and target is not in the retro prediction:
        
            #     print(target_predictions.shape)
            #     target_predictions_Forw_val = target_predictions[target_predictions['Target'] == target_predictions['Forward_Prediction']]
            #     print(target_predictions_Forw_val.shape)
            #     target_predictions_Forw_val = target_predictions_Forw_val[target_predictions_Forw_val['Target'] != target_predictions_Forw_val['Retro']]
            #     print(target_predictions_Forw_val.shape)        

            #     #target_predictions_Forw_val['rxn_string'] =target_predictions_Forw_val.apply(lambda s: s['Retro'] + '>' + s['Reagents'] + '>' + target_smile,axis=1)
            #     #target_predictions_Forw_val['rxn_class'] = target_predictions_Forw_val.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)
            #     print(target_predictions_Forw_val.shape) 
            #     result_filtered = check_reactant_exact_match(target_predictions_Forw_val, canonicalize_smiles(precusor_1_smile))
            #     print(f"Loaded {len(target_predictions_Forw_val[result_filtered])} match records from fileStorage")
    