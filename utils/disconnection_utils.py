import pandas as pd
import re
from scscore import SCScorer
scs_scorer = SCScorer()

def check_for_heavy_elements(smiles_str: str, max_atomic_number: int = 30) -> set:
    """Check for heavy elements in a SMILES string."""
    element_pattern = r'(?:\[([A-Z][a-z]?).*?\])|(?<![a-zA-Z])([A-Z][a-z]?)(?![a-zA-Z])'
    
    HEAVY_ELEMENTS = {
    'Ga': 31, 'Ge': 32, 'As': 33, 'Se': 34, 'Br': 35, 'Kr': 36, 'Rb': 37, 'Sr': 38, 'Y': 39, 'Zr': 40,
    'Nb': 41, 'Mo': 42, 'Tc': 43, 'Ru': 44, 'Rh': 45, 'Pd': 46, 'Ag': 47, 'Cd': 48, 'In': 49, 'Sn': 50,
    'Sb': 51, 'Te': 52, 'I': 53, 'Xe': 54, 'Cs': 55, 'Ba': 56, 'La': 57, 'Ce': 58, 'Pr': 59, 'Nd': 60,
    'Pm': 61, 'Sm': 62, 'Eu': 63, 'Gd': 64, 'Tb': 65, 'Dy': 66, 'Ho': 67, 'Er': 68, 'Tm': 69, 'Yb': 70,
    'Lu': 71, 'Hf': 72, 'Ta': 73, 'W': 74, 'Re': 75, 'Os': 76, 'Ir': 77, 'Pt': 78, 'Au': 79, 'Hg': 80,
    'Tl': 81, 'Pb': 82, 'Bi': 83, 'Po': 84, 'At': 85, 'Rn': 86, 'Fr': 87, 'Ra': 88, 'Ac': 89, 'Th': 90,
    'Pa': 91, 'U': 92
    }
    
    found_elements = set()
    matches = re.finditer(element_pattern, smiles_str)
    
    for match in matches:
        element = match.group(1) if match.group(1) else match.group(2)
        if element in HEAVY_ELEMENTS and HEAVY_ELEMENTS[element] > max_atomic_number:
            found_elements.add(element)
    
    return found_elements

def get_max_reactant_score(reactants_str: str):
    """Check if reagents contain heavy elements."""
    components = reactants_str.split('.')
    max_reactant_score= -1.0
    
    for component in components:
        temp_score = 1-((scs_scorer.get_score_from_smi(component)[1]-1)/4)
        if temp_score > max_reactant_score:
            max_reactant_score = temp_score
    return max_reactant_score

def should_filter_reagent(reagents_str: str):
    """Check if reagents contain heavy elements."""
    components = reagents_str.split('.')
    all_found_elements = set()
    
    for component in components:
        heavy_elements =check_for_heavy_elements(component)
        all_found_elements.update(heavy_elements)
    
    return (len(all_found_elements) > 0, all_found_elements)

def is_clean_reagent(row):
    reagent_set = f"{row['Reagents']}.{row['Retro']}"
    should_filter, all_found_elements = should_filter_reagent(reagent_set)
    # print(f"should_filter={should_filter}")
    # print(all_found_elements)
    if should_filter:
        print(f"found heavy metals: {all_found_elements}")
        return False
    return True
