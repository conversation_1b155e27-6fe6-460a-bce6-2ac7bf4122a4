import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from api.singleStepRetroRelaxed_api import *
from api.singleStepRetroRelaxed_api import *
from api.synthesis_score_api import *
from config.settings import Config

import json
def singleStepRetro_runner(target_smiles):
    """Example of how to use the TreeSearchEngine."""
    
    #Assuming you have your SingleStepRetroAPI instance
    pre_loaded_retro_models = 0
    if pre_loaded_retro_models ==1:
        model_path = "assets/mttl_models/USPTO_STEREO_separated_T0_AutoTag_260000.pt"
        AutoTagging_Beam_Size=50
        transformer_AutoTag = load_onmt_transformer(model_path,beam_size=AutoTagging_Beam_Size)
        
        Retro_beam_size=3
        model_path = "assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt"
        transformer_T1 = load_onmt_transformer(model_path,beam_size=Retro_beam_size)

        USPTO_Reag_Beam_Size=3
        model_path = "assets/mttl_models/USPTO_STEREO_separated_T2_Reagent_Pred_225000.pt"
        transformer_T2 = load_onmt_transformer(model_path,beam_size=USPTO_Reag_Beam_Size)

        model_path = "assets/mttl_models/USPTO_STEREO_separated_T3_Forward_255000.pt"
        transformer_T3 = load_onmt_transformer(model_path,beam_size=3)    
    else:
        transformer_AutoTag = None
        transformer_T1 = None
        transformer_T2 = None
        transformer_T3 = None    

    single_step_api = SingleStepRetroRelaxedAPI(transformer_AutoTag,transformer_T1,transformer_T2, transformer_T3)
    target_predictions = single_step_api.get_retrosynthesis_reactions(target_smiles)
    return 
    

def main():

    with open("input/precursors_simple.json", "r") as f:
        data = json.load(f)

    # Loop through molecules and their contents
    for molecule_key in data:
        print(f"Molecule: {molecule_key}")
        for compound_key in data[molecule_key]:
            print(f"{compound_key}: {data[molecule_key][compound_key]}")
            target_smiles = data[molecule_key][compound_key]
            print(target_smiles)
            singleStepRetro_runner(target_smiles)

if __name__ == "__main__":
    main()