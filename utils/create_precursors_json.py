import pandas as pd
import json
import re

def parse_reaction_string(reaction_str):
    """
    Parse a reaction string in format: 'precursors>reagents>products'
    Returns the first precursor and first product
    """
    if '>>' in reaction_str:
        # Handle reactions with >> format (direct transformation)
        parts = reaction_str.split('>>')
        precursor = parts[0].split('.')[0].strip()  # Take first precursor
        product = parts[1].split('.')[0].strip()    # Take first product
    else:
        # Handle reactions with > format (with reagents)
        parts = reaction_str.split('>')
        if len(parts) >= 3:
            precursor = parts[0].split('.')[0].strip()  # Take first precursor
            product = parts[2].split('.')[0].strip()    # Take first product
        else:
            precursor = parts[0].split('.')[0].strip()
            product = parts[-1].split('.')[0].strip()
    
    return precursor, product

def create_precursors_json_with_all_steps(csv_file_path):
    """
    Create a JSON structure mapping molecules to their targets and all step precursors
    """
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    
    # Clean column names (remove any whitespace)
    df.columns = df.columns.str.strip()
    
    # Group by molecules
    grouped = df.groupby('molecules')
    
    precursors = {}
    molecule_counter = 1
    
    for molecule_name, group in grouped:
        molecule_key = f"molecule_{molecule_counter}"
        
        # Sort steps to ensure proper order (Step 1, Step 2, etc.)
        group_sorted = group.sort_values('Steps')
        
        # Get the target molecule (product of Step 1)
        step1_reaction = group_sorted.iloc[0]['ReactionStrng']
        _, target_molecule = parse_reaction_string(step1_reaction)
        
        # Initialize the molecule entry with target
        precursors[molecule_key] = {
            "Target": target_molecule
        }
        
        # Add precursors from each step
        precursor_counter = 1
        for _, row in group_sorted.iterrows():
            precursor, product = parse_reaction_string(row['ReactionStrng'])
            precursor_key = f"Precursor_{precursor_counter}"
            precursors[molecule_key][precursor_key] = precursor
            precursor_counter += 1
        
        molecule_counter += 1
    
    return precursors

def create_precursors_json_with_step_details(csv_file_path):
    """
    Create a more detailed JSON structure that includes step information
    """
    # Read the CSV file
    df = pd.read_csv(csv_file_path)
    
    # Clean column names (remove any whitespace)
    df.columns = df.columns.str.strip()
    
    # Group by molecules
    grouped = df.groupby('molecules')
    
    precursors = {}
    molecule_counter = 1
    
    for molecule_name, group in grouped:
        molecule_key = f"molecule_{molecule_counter}"
        
        # Sort steps to ensure proper order (Step 1, Step 2, etc.)
        group_sorted = group.sort_values('Steps')
        
        # Get the target molecule (product of Step 1)
        step1_reaction = group_sorted.iloc[0]['ReactionStrng']
        _, target_molecule = parse_reaction_string(step1_reaction)
        
        # Initialize the molecule entry
        precursors[molecule_key] = {
            "Target": target_molecule,
            "Steps": {}
        }
        
        # Add detailed information for each step
        for _, row in group_sorted.iterrows():
            precursor, product = parse_reaction_string(row['ReactionStrng'])
            step_name = row['Steps']
            
            precursors[molecule_key]["Steps"][step_name] = {
                "Precursor": precursor,
                "Product": product,
                "Reaction": row['ReactionStrng']
            }
        
        # Also add flat precursor structure for compatibility
        precursor_counter = 1
        for _, row in group_sorted.iterrows():
            precursor, _ = parse_reaction_string(row['ReactionStrng'])
            precursor_key = f"Precursor_{precursor_counter}"
            precursors[molecule_key][precursor_key] = precursor
            precursor_counter += 1
        
        molecule_counter += 1
    
    return precursors

def main():
    # Process the CSV file
    csv_file = 'input/chemValidations.csv'  # Update with your file path
    
    try:
        print("=== Simple Format (All Precursors) ===")
        precursors_simple = create_precursors_json_with_all_steps(csv_file)
        print("precursors =", json.dumps(precursors_simple, indent=4))
        
        print("\n" + "="*50)
        print("=== Detailed Format (With Step Info) ===")
        precursors_detailed = create_precursors_json_with_step_details(csv_file)
        print("precursors_detailed =", json.dumps(precursors_detailed, indent=4))
        
        # Save both formats
        with open('precursors_simple.json', 'w') as f:
            json.dump(precursors_simple, f, indent=4)
        
        with open('precursors_detailed.json', 'w') as f:
            json.dump(precursors_detailed, f, indent=4)
        
        print(f"\nFiles saved:")
        print(f"- precursors_simple.json")
        print(f"- precursors_detailed.json")
        
    except FileNotFoundError:
        print(f"Error: Could not find the CSV file '{csv_file}'. Please check the file path.")
    except Exception as e:
        print(f"Error processing the file: {e}")

# Quick function to get just the simple format
def get_all_precursors_dict(csv_file_path):
    """
    Returns dictionary with all precursors for each molecule
    """
    df = pd.read_csv(csv_file_path)
    df.columns = df.columns.str.strip()
    
    grouped = df.groupby('molecules')
    precursors = {}
    molecule_counter = 1
    
    for molecule_name, group in grouped:
        molecule_key = f"molecule_{molecule_counter}"
        group_sorted = group.sort_values('Steps')
        
        # Target from Step 1 product
        step1_reaction = group_sorted.iloc[0]['ReactionStrng']
        _, target_molecule = parse_reaction_string(step1_reaction)
        
        # Initialize with target
        precursors[molecule_key] = {"Target": target_molecule}
        
        # Add all precursors
        precursor_counter = 1
        for _, row in group_sorted.iterrows():
            precursor, _ = parse_reaction_string(row['ReactionStrng'])
            precursor_key = f"Precursor_{precursor_counter}"
            precursors[molecule_key][precursor_key] = precursor
            precursor_counter += 1
        
        molecule_counter += 1
    
    return precursors

if __name__ == "__main__":
    main()

# Usage examples:
# For simple format: precursors = get_all_precursors_dict('chemValidations  Sheet1 1.csv')
# For detailed format: precursors = create_precursors_json_with_step_details('chemValidations  Sheet1 1.csv')