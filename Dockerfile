
FROM python:3.8.16

WORKDIR /app

# Environment variables

# Copy and install dependencies
COPY . .

RUN pip install --no-cache-dir -r requirements.txt

# Copy the zipped assets and the rest of the app


# Unzip assets.zip into the /app/assets directory
RUN apt-get update && apt-get install -y unzip && \
    mkdir -p temp && \
    unzip assets.zip -d temp && \
    mkdir -p assets && \
    cp -r temp/assets/* ./assets/ && \
    rm -rf temp assets.zip

RUN mkdir -p /app/output
# Fix line endings and add execute permission
RUN sed -i 's/\r$//' start.sh && chmod +x start.sh

EXPOSE 8030

CMD ["/bin/sh", "./start.sh"]
