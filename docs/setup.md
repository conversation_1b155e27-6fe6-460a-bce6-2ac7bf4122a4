#installation notes

# Create a new environment
# Initialize conda for your shell (bash in this example)
eval "$($HOME/miniconda/bin/conda shell.bash hook)"
azure-gpu
eval "$($HOME/miniconda3/bin/conda shell.bash hook)"

conda create -n chemStackRetro python=3.8.16
conda activate chemStackRetro


# Install the specific dependencies
conda install -c conda-forge numpy=1.19.5
conda install -c conda-forge pandas=1.1.5
conda install -c conda-forge rdkit=2022.3.3

pip install -r requirements.txt

##
numpy==1.19.5
pandas==1.1.5
rdkit==2022.3.3
psycopg2-binary
pydantic
pyarrow
sqlalchemy
python-dotenv
redis

##
pip install git+https://github.com/reymond-group/OpenNMT-py@a006d8ec25cd138fce466d9d3ab24bd1c915a750
pip install git+https://github.com/DavKre/scscore.git@8cc8b9fbc8150d608d9632b28c1c7c3148433115

sudo mkdir -p /opt/disconnections/models
sudo chown -R deepak:deepak /opt/disconnections

sudo mkdir -p /opt/disconnections_relaxed/models
sudo chown -R deepak:deepak /opt/disconnections_relaxed
sudo chown -R deepak:deepak /opt/disconnections_relaxed/models

python retro_runner.py 
python retro_runner_relaxed.py 
python utils/offline_singleSteps.py

