import os
import json
import logging
import traceback
import time
import socket
import redis
from http import HTT<PERSON>tatus
from http.server import HTTPServer, BaseHTTPRequestHandler
from http.server import ThreadingHT<PERSON>Server
from config.settings import Config
from celery.schedules import crontab
from celery import Celery
from celery.signals import after_setup_logger, worker_process_init
from threading import Thread, Lock
from db.db_ops import DbOps, RetroStatus
import copy
from datetime import datetime, timezone
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from utils.common import get_reaction_smiles_data


db_ops = DbOps()
app_config = Config()
# Logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Thread coordination
thread_lock = Lock()

# Celery configuration

enrich_app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
enrich_app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    worker_max_tasks_per_child=1,
    task_default_queue=app_config.ENRICH_OUTPUT_REDIS_QUEUE,  # Add this line
    task_routes={
        'process_enrich_inflow_task': {'queue': app_config.ENRICH_OUTPUT_REDIS_QUEUE},
    }
)

@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    os.makedirs('logs', exist_ok=True)
    fh = logging.FileHandler('logs/enrich.log')
    fh.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(fh)


def enrich_data(route_id , metadata):
    """Enriches the top 100 routes with additional reaction data."""
    

    top_routes = db_ops.get_route_by_id(route_id)

    if not top_routes:
        return
    reaction_class  = {}
    for reaction_data in metadata:
        reaction_class[reaction_data.get('reaction_smiles')] = reaction_data.get('other_info')

    for route in top_routes:
        for step in route.get('data', []):
            reaction_string = step.get('reaction_string')
            if reaction_string:
                step['other_information'] = reaction_class.get(reaction_string, {})

    
    try:
        db_ops.update_retro_data(route_id, route['data'])
        logger.info(f"[CRON Enrich] Updated route {route_id} in database")
    except Exception as e:
        logger.error(f"[CRON Enrich] Failed to update route {route_id}: {e}")




@enrich_app.task(bind=True, max_retries=3, name='process_enrich_inflow_task')
def process_enrich_inflow_task(self, payload):
    start_time = time.time()
    required_fields = {"route_id"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        error_msg = f"Missing required fields: {missing}"
        logger.error(error_msg)
        return False
    
    route_id = str(payload["route_id"])
    enrich_data(route_id, payload.get('other_info'))
    return True


