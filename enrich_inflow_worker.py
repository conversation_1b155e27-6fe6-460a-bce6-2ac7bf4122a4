import os
import json, uuid
from config.settings import Config
from celery import Celery

# Initialize Celery app (same config as your worker)
app_config = Config()
if app_config.REDIS_SENTINEL_URL:
    app_config.REDIS_URL = app_config.REDIS_SENTINEL_URL

celery_infow_app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
if app_config.REDIS_SENTINEL_URL:
    celery_infow_app.conf.broker_transport_options = {
        "master_name": app_config.REDIS_SENTINEL_SERVICE_NAME,
    }
def send_enrich_inflow_task(payload):
    """Send a task to the patent worker"""
    INPUT_REDIS_QUEUE = os.getenv("ENRICH_INPUT_REDIS_QUEUE", "enrich_input_job_queue")
    
    # Send task to specific queue
    result = celery_infow_app.send_task(
        'process_enrich_task',
        args=[payload],
        queue=INPUT_REDIS_QUEUE
    )
    return result
