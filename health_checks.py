


from http.server import BaseHT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPServer
import json
import time
import socket
import logging
from http import HTT<PERSON>tatus
from config.settings import Config
import redis
from threading import Thread
from http.server import ThreadingHTTPServer

app_config = Config()
# Logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class RedisClient:
    def __init__(self, redis_url):
        self.redis = redis.Redis.from_url(redis_url, decode_responses=True)
        self.redis.ping()

    def insert_into_output_queue(self, data):
        self.redis.rpush(app_config.RETRO_OUTPUT_REDIS_QUEUE, json.dumps(data))

    def get_queue_length(self, queue_name):
        return self.redis.llen(queue_name)

class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            try:
                client = RedisClient(app_config.REDIS_URL)
                data = {
                    "status": "healthy",
                    "output_queue_length": client.get_queue_length(app_config.RETRO_OUTPUT_REDIS_QUEUE),
                    "timestamp": time.time()
                }
            except Exception as e:
                data = {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": time.time()
                }

            try:
                self.send_response(HTTPStatus.OK)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(data).encode())
            except (BrokenPipeError, ConnectionResetError, socket.error) as e:
                logger.warning(f"[HEALTH] Client disconnected before response could be sent: {e}")

    def log_message(self, format, *args):
        return
    

def run_health_server():
    logger.info("[HEALTH] Starting health check server...")
    server = ThreadingHTTPServer(('0.0.0.0', 8030), HealthCheckHandler)
    logger.info("[HEALTH] Health check server running on port 8030")
    server.serve_forever()


run_health_server()