import hashlib
import os
import tempfile

from dotenv import load_dotenv
from indigo import Indigo
from indigo.renderer import IndigoRenderer
from azure.storage.blob import BlobServiceClient
from config.settings import Config


load_dotenv()

class AzureUtils:
    """
    Azure utility class for handling chemical reactions and rendering images. and store it in Azure Blob Storage.
    This class provides methods to load chemical reactions, render them as images, and upload these images to Azure Blob Storage.
    Attributes:
    ----------
    indigo : Indigo
        An instance of the Indigo library for chemical informatics.
    renderer : IndigoRenderer
        An instance of the IndigoRenderer for rendering chemical structures.
   
    """
    def __init__(self):
        """
        Initializes the AzureUtils class by creating instances of Indigo and IndigoRenderer.
        """
        self.indigo = Indigo()
        self.config = Config()
        self.renderer = IndigoRenderer(self.indigo)
        self.connection_string = self.config.AZURE_BLOB_CONNECTION_STRING
        self.container_name = self.config.AZURE_BLOB_CONTAINER_NAME


    def upload_bytes_to_azure_blob(self, image_bytes: bytes, blob_name: str) -> str:
        blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        container_client = blob_service_client.get_container_client(self.container_name)

        blob_client = container_client.get_blob_client(blob_name)
        blob_client.upload_blob(image_bytes, overwrite=True, content_type='image/png')  # Set content type for direct rendering

        blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{self.container_name}/{blob_name}"
        return blob_url

    def render_reaction_to_image(self, reaction_smiles: str) -> bytes:
        reaction = self.indigo.loadReaction(reaction_smiles)
        
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            self.renderer.renderToFile(reaction, tmp_file.name)
            tmp_file.seek(0)
            image_bytes = tmp_file.read()
        
        return image_bytes
    
    def image_to_blob(self, reaction_smiles: str) -> str:
        """
        Uploads an image to Azure Blob Storage.

        Parameters:
        ----------
        image_bytes : bytes
            The image data in bytes.
        blob_name : str
            The name of the blob in Azure Blob Storage.
        connection_string : str
            The connection string for Azure Blob Storage.
        container_name : str
            The name of the container in Azure Blob Storage.

        Returns:
        -------
        str
            The URL of the uploaded blob.
        """
        try:
            image_bytes = self.render_reaction_to_image(reaction_smiles)
            blob_name = hashlib.md5(reaction_smiles.encode()).hexdigest() + ".png"
            return self.upload_bytes_to_azure_blob(image_bytes, blob_name)
        except Exception as e:
            print(f"Error rendering or uploading image: {e}")
            return None