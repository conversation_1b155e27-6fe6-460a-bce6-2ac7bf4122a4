# chemstack_retrosynthesis
A comprehensive multi-step retro-synthesis tree search system with the following key features:
Architecture & Design Patterns
1. Modular Design with SOLID Principles

Single Responsibility: Each class has a single, well-defined purpose
Open/Closed: Extensible through interfaces (validators, scorers, checkers)
Dependency Inversion: Uses abstractions for all external dependencies

2. Key Components
Tree Nodes:

MoleculeNode (OR node): Solved if any reaction path works
ReactionNode (AND node): Solved only if all precursors are solved

Validators (Extensible):

DisconnectionValidator: Validates reactions (currently forward probability > 0.7)
TerminalChecker: Determines when molecules are "solved" (depth + synthesis score)

Scoring System:

RouteScorer: Pluggable scoring strategies for ranking routes
Currently scores based on total synthesis scores of all reactants

3. Key Features
✅ AND/OR Logic: Proper handling of reaction (AND) and molecule (OR) nodes
✅ Extensible Validation: Easy to add new disconnection validity criteria
✅ Flexible Terminal Conditions: Supports depth limits and synthesis score thresholds
✅ Modular Scoring: Pluggable route scoring strategies
✅ Celery/Redis Ready: Structured for distributed processing
✅ Local Mode Support: Can run without Redis/Celery
4. Tree Search Algorithm
The search uses breadth-first expansion with the following logic:

Molecule Node Expansion:

Check if terminal (depth/synthesis score)
Get disconnections from single-step API
Create reaction nodes for valid disconnections


Reaction Node Expansion:

Parse reactants from reaction
Create molecule nodes for each reactant
Mark as solved when all reactants are solved


Route Extraction:

DFS traversal to find complete paths
Score and rank all valid routes
