import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import json
from sqlalchemy import create_engine, text
import logging

import os
from dotenv import load_dotenv
load_dotenv()  # take environment variables from .env.

DB_URL = os.getenv("DB_URL")
DB_USERNAME = os.getenv("DB_USERNAME")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")

# Database configuration
DB_CONFIG = {
    'host': DB_URL,
    'database': DB_NAME,
    'user': DB_USERNAME,
    'password': DB_PASSWORD,  # Add your password here
    'port': 5432
}

def create_disconnections_table():
    """
    Creates a table to store retrosynthesis dataframes in PostgreSQL.
    
    Returns:
        bool: True if table created successfully, False otherwise
    """
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # SQL to create table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS disconnections (
            id SERIAL PRIMARY KEY,
            t1_model TEXT,
            analysis_id VARCHAR(50),
            id_tag INTEGER,
            id_beam INTEGER,
            target TEXT,
            tagged_target TEXT,
            retro TEXT,
            retro_conf REAL,
            forward_model TEXT,
            reagents TEXT,
            forward_prediction TEXT,
            prob_forward_prediction_1 REAL,
            prob_forward_prediction_2 REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Create indexes for commonly queried columns
        CREATE INDEX IF NOT EXISTS idx_retrosynthesis_analysis_id ON disconnections(analysis_id);
        CREATE INDEX IF NOT EXISTS idx_retrosynthesis_target ON disconnections(target);
        CREATE INDEX IF NOT EXISTS idx_retrosynthesis_created_at ON disconnections(created_at);
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        
        print("Table 'disconnections' created successfully!")
        return True
        
    except Exception as e:
        print(f"Error creating table: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def create_patent_disconnections_table():
    """
    Creates a table to store retrosynthesis dataframes in PostgreSQL.
    
    Returns:
        bool: True if table created successfully, False otherwise
    """
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # SQL to create table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS patent_disconnections (
            id SERIAL PRIMARY KEY,
            t1_model TEXT,
            analysis_id VARCHAR(50),
            id_tag INTEGER,
            id_beam INTEGER,
            target TEXT,
            tagged_target TEXT,
            retro TEXT,
            retro_conf REAL,
            forward_model TEXT,
            reagents TEXT,
            forward_prediction TEXT,
            prob_forward_prediction_1 REAL,
            prob_forward_prediction_2 REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Create indexes for commonly queried columns
        CREATE INDEX IF NOT EXISTS idx_retrosynthesis_analysis_id ON patent_disconnections(analysis_id);
        CREATE INDEX IF NOT EXISTS idx_retrosynthesis_target ON patent_disconnections(target);
        CREATE INDEX IF NOT EXISTS idx_retrosynthesis_created_at ON patent_disconnections(created_at);
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        
        print("Table 'patent_disconnections' created successfully!")
        return True
        
    except Exception as e:
        print(f"Error creating table: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


def insert_dataframe_to_postgres(df, table_name='disconnections', batch_size=1000):
    """
    Inserts a pandas DataFrame into the PostgreSQL table using psycopg2 directly.
    
    Args:
        df (pd.DataFrame): DataFrame to insert
        table_name (str): Name of the target table
        batch_size (int): Number of rows to insert per batch
    
    Returns:
        bool: True if insertion successful, False otherwise
    """
    try:
        # Connect directly with psycopg2 to avoid SQLAlchemy schema conflicts
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Prepare DataFrame - rename columns to match database schema
        df_copy = df.copy()
        
        # Column mapping from DataFrame to database
        column_mapping = {
            'T1_Model': 't1_model',
            'ID': 'analysis_id',
            'ID_Tag': 'id_tag',
            'ID_beam': 'id_beam',
            'Target': 'target',
            'Tagged_Target': 'tagged_target',
            'Retro': 'retro',
            'Retro_Conf': 'retro_conf',
            'Forward_Model': 'forward_model',
            'Reagents': 'reagents',
            'Forward_Prediction': 'forward_prediction',
            'Prob_Forward_Prediction_1': 'prob_forward_prediction_1',
            'Prob_Forward_Prediction_2': 'prob_forward_prediction_2'
        }
        
        # Drop the 'index' column if it exists (it's not needed in the database)
        if 'index' in df_copy.columns:
            df_copy = df_copy.drop('index', axis=1)
            print("Dropped 'index' column from DataFrame")
        
        # Only rename columns that exist in the DataFrame and are in our mapping
        existing_columns = {k: v for k, v in column_mapping.items() if k in df_copy.columns}
        df_copy = df_copy.rename(columns=existing_columns)
        
        # Only keep columns that have a mapping (filter out any unexpected columns)
        expected_db_columns = list(column_mapping.values())
        df_copy = df_copy[[col for col in df_copy.columns if col in expected_db_columns]]
        
        print(f"Final DataFrame columns for insertion: {list(df_copy.columns)}")
        
        # Replace NaN values with None for proper NULL insertion
        df_copy = df_copy.where(pd.notnull(df_copy), None)
        
        # Get column names for the insert statement
        columns = list(df_copy.columns)
        columns_str = ', '.join(columns)
        
        # For execute_values, we need a template with only one %s
        insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES %s"
        
        print(f"Insert SQL: {insert_sql}")
        
        # Convert DataFrame to list of tuples
        data_tuples = [tuple(row) for row in df_copy.values]
        
        # Insert data in batches using execute_values
        for i in range(0, len(data_tuples), batch_size):
            batch = data_tuples[i:i + batch_size]
            execute_values(
                cursor,
                insert_sql,
                batch,
                page_size=min(batch_size, len(batch))
            )
        
        conn.commit()
        print(f"Successfully inserted {len(data_tuples)} rows into {table_name}")
        return True
        
    except Exception as e:
        print(f"Error inserting data: {e}")
        import traceback
        print(f"Full error traceback: {traceback.format_exc()}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def insert_dataframe_batch_psycopg2(df, table_name='retrosynthesis_results', batch_size=1000):
    """
    Alternative method using psycopg2 for potentially better performance on large datasets.
    
    Args:
        df (pd.DataFrame): DataFrame to insert
        table_name (str): Name of the target table
        batch_size (int): Number of rows to insert per batch
    
    Returns:
        bool: True if insertion successful, False otherwise
    """
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Prepare data
        df_copy = df.copy()
        
        # Column mapping
        column_mapping = {
            'T1_Model': 't1_model',
            'ID': 'analysis_id',
            'ID_Tag': 'id_tag',
            'ID_beam': 'id_beam',
            'Target': 'target',
            'Tagged_Target': 'tagged_target',
            'Retro': 'retro',
            'Retro_Conf': 'retro_conf',
            'Forward_Model': 'forward_model',
            'Reagents': 'reagents',
            'Forward_Prediction': 'forward_prediction',
            'Prob_Forward_Prediction_1': 'prob_forward_prediction_1',
            'Prob_Forward_Prediction_2': 'prob_forward_prediction_2'
        }
        
        df_copy = df_copy.rename(columns=column_mapping)
    
        
        # Prepare insert statement
        columns = list(df_copy.columns)
        insert_sql = f"""
        INSERT INTO {table_name} ({', '.join(columns)}) 
        VALUES %s
        """
        
        # Convert DataFrame to list of tuples
        data_tuples = [tuple(row) for row in df_copy.values]
        
        # Execute batch insert
        execute_values(
            cursor,
            insert_sql,
            data_tuples,
            template=None,
            page_size=batch_size
        )
        
        conn.commit()
        print(f"Successfully inserted {len(data_tuples)} rows using batch method")
        return True
        
    except Exception as e:
        print(f"Error in batch insert: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def query_disconnections_data(table_name='disconnections', analysis_id=None, target=None):#, limit=100):
    """
    Query function to retrieve data from the retrosynthesis table.
    
    Args:
        table_name (str): Name of the table to query
        analysis_id (str): Filter by analysis ID
        target (str): Filter by target molecule
        min_score (float): Minimum score threshold
        limit (int): Maximum number of results to return
    
    Returns:
        pd.DataFrame: Query results
    """
    try:
        # Use psycopg2 directly to avoid SQLAlchemy schema conflicts
        conn = psycopg2.connect(**DB_CONFIG)
        
        # Build query
        where_conditions = []
        params = []
        
        base_query = f"SELECT * FROM {table_name}"
        
        if analysis_id:
            where_conditions.append("analysis_id = %s")
            params.append(analysis_id)
            
        if target:
            where_conditions.append("target = %s")
            params.append(target)
            
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        query = f"""
        {base_query}
        {where_clause}
        ORDER BY created_at DESC
        """
        #params.append(limit)
        
        # Execute query and fetch results
        df = pd.read_sql(query, conn, params=params)
        
        return df
        
    except Exception as e:
        print(f"Error querying data: {e}")
        import traceback
        print(f"Full error traceback: {traceback.format_exc()}")
        return pd.DataFrame()
    finally:
        if 'conn' in locals():
            conn.close()

def query_disconnections_target(target=None):
    """
    Query function to retrieve data from the retrosynthesis table.
    
    Args:
        table_name (str): Name of the table to query
        analysis_id (str): Filter by analysis ID
        target (str): Filter by target molecule
        min_score (float): Minimum score threshold
        limit (int): Maximum number of results to return
    
    Returns:
        pd.DataFrame: Query results
    """
    try:
        # Use psycopg2 directly to avoid SQLAlchemy schema conflicts
        conn = psycopg2.connect(**DB_CONFIG)

        query = f"""
        SELECT 
        id as index,
        t1_model as T1_Model,
        analysis_id as ID,
        id_tag as ID_Tag,
        id_beam as ID_beam,
        target as Target,
        tagged_target as Tagged_Target,
        retro as Retro,
        retro_conf as Retro_Conf,
        forward_model as Forward_Model,
        reagents as Reagents,
        forward_prediction as Forward_Prediction,
        prob_forward_prediction_1 as Prob_Forward_Prediction_1,
        prob_forward_prediction_2 as Prob_Forward_Prediction_2
        FROM disconnections
        WHERE target = '{target}'
        ORDER BY Prob_Forward_Prediction_1 DESC
        """
        
        # Execute query and fetch results
        df = pd.read_sql(query, conn)
        
        return df
        
    except Exception as e:
        print(f"Error querying data: {e}")
        import traceback
        print(f"Full error traceback: {traceback.format_exc()}")
        return pd.DataFrame()
    finally:
        if 'conn' in locals():
            conn.close()


# Example usage
if __name__ == "__main__":
    # 1. Create the table
    #create_disconnections_table()
    create_patent_disconnections_table()
    
    
    # 2. Example of inserting a DataFrame (you would replace this with your actual DataFrame)
    
    # # Assuming you have a DataFrame called 'df' with your retrosynthesis data
    # df = pd.read_parquet("/home/<USER>/chemstack_retro/target_predictions_test.parquet")
    # print(df.shape)
    # print(df.columns)
    # success = insert_dataframe_to_postgres(df)
    # if success:
    #     print("Data inserted successfully!")
    # else:
    #     print("Failed to insert data")
    
    # 3. Query some data
    #results = query_disconnections_target('O=C(O)C1=CC(Cl)=CC(=C1N)C')
    # results = query_disconnections_data()
    # print(f"Found {len(results)} high-scoring results")
    # print(results.shape)
    # print(results.head())
    # print(results.iloc[0])
    
