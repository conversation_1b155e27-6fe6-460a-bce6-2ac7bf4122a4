from sqlalchemy import create_engine, inspect
import os
import pandas as pd
from dotenv import load_dotenv
load_dotenv()  

import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import json
from sqlalchemy import create_engine, text
import logging

import os
from dotenv import load_dotenv
load_dotenv()  # take environment variables from .env.

DB_URL = os.getenv("DB_URL")
DB_USERNAME = os.getenv("DB_USERNAME")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")

# Database configuration
DB_CONFIG = {
    'host': DB_URL,
    'database': DB_NAME,
    'user': DB_USERNAME,
    'password': DB_PASSWORD,  # Add your password here
    'port': 5432
}

def query_disconnections(query):

    try:
        # Use psycopg2 directly to avoid SQLAlchemy schema conflicts
        conn = psycopg2.connect(**DB_CONFIG)

        # Execute query and fetch results
        df = pd.read_sql(query, conn)
        
        return df
        
    except Exception as e:
        print(f"Error querying data: {e}")
        import traceback
        print(f"Full error traceback: {traceback.format_exc()}")
        return pd.DataFrame()
    finally:
        if 'conn' in locals():
            conn.close()


# Example usage
if __name__ == "__main__":

    #1. get total disconnections
    print("Total disconnections in DB")
    query = f"SELECT count(*) FROM disconnections"
    results = query_disconnections(query)
    print(results.head())

    #2. get total disconnections group by target
    print("Total target molecules in DB")    
    query = f"SELECT COUNT(DISTINCT target) AS num_distinct_targets FROM disconnections"
    results = query_disconnections(query)
    print(results.shape)
    print(results.head())

    #2. get total disconnections group by target
    print("Total disconnections per target in DB")    
    query = "SELECT target, count(*) FROM disconnections group by target"
    results = query_disconnections(query)
    print(results.shape)
    print(results.head())

    #4. download total disconnections
    print("Total disconnections in DB")
    query = f"SELECT * FROM disconnections"
    results = query_disconnections(query)
    print(results.head())
    results.to_csv("db_disconnections_5June534am.csv")


