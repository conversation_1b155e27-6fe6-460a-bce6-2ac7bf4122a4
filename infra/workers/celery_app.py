from celery import Celery
from config.settings import Config

def create_celery_app():
    """Create and configure Celery app."""
    celery_app = Celery(
        'retro_synthesis_tree',
        broker=Config.CELERY_BROKER_URL,
        backend=Config.CELERY_RESULT_BACKEND,
        include=['workers.tasks']
    )
    
    celery_app.conf.update(
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
    )
    
    return celery_app