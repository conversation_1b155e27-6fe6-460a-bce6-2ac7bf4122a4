from typing import List
from typing import Tuple, Set
import datetime
import sys
from onmt.utils.parse import ArgumentParser
import onmt.bin.translate as trsl 
from onmt.translate.translator import build_translator
from onmt.opts import translate_opts
from argparse import ArgumentParser
from types import SimpleNamespace
import hashlib
import os

def hash_smiles(smiles: str) -> str:
    return hashlib.md5(smiles.encode()).hexdigest()[:8]

def tokenize_smiles(smiles: str) -> str:
    """Convert SMILES to space-separated character format required by the model"""
    return ' '.join(list(smiles))

def detokenize_smiles(tokenized_smiles: str) -> str:
    """Convert space-separated characters back to SMILES"""
    return ''.join(tokenized_smiles.split())


def load_onmt_transformer(model_path, beam_size=3, batch_size: int = 64):
    # Prepare Input File names for OpenNMT:
    timestamp = str(datetime.datetime.now()).replace(' ', '__').replace('-', '_').replace(':', '#')
    output_file = 'output/onmt/output__{}.txt'.format(timestamp)   

    opt = SimpleNamespace(
        models=[model_path],
        data_type="text",
        beam_size=beam_size,
        n_best=beam_size,
        batch_size=batch_size,
        max_length=1000,
        replace_unk=True,
        log_probs=True,
        report_score=True,
        verbose=False,
        gpu=-1,
        output=output_file,

        # Scorer
        alpha=0.0,
        beta=0.0,
        coverage_penalty="none",
        length_penalty="none",

        # Required by translator
        fp32=False,
        avg_raw_probs=False,
        report_align=False,

        # Optional extras
        min_length=0,
        ratio=0.0,
        attn_debug=False,
        align_debug=False,
        dump_beam="",
        block_ngram_repeat=0,
        ignore_when_blocking=[],
        phrase_table="",
        window_size=0,
        window_stride=0,
        window="hamming",
        image_channel_size=3,
        seed=829,
        random_sampling_topk=1,
        random_sampling_temp=1.0,
        batch_type="sents",
        attn_type="",
        dump_fields=False,
        tgt=None,
        tgt_prefix=False,
        shard_size=0,
        dynamic_dict=False,
        share_vocab=False,
        sample_rate=0,
        log_file="",
        log_file_level="INFO",
        max_sent_length=None,
        stepwise_penalty=False,
        report_time=False
    )
    transformer = build_translator(opt, report_score=True)
    return transformer

def process_predictions(predictions_raw, scores_raw, beam_size, untokenize_output=True):
    """
    Process predictions to return results organized by input (not by beam rank)
    """
    num_inputs = len(predictions_raw)
    predictions_by_input = []
    probs_by_input = []

    for i, (beam_list, score_list) in enumerate(zip(predictions_raw, scores_raw)):
        input_predictions = []
        input_probs = []
        
        for j in range(min(beam_size, len(beam_list))):
            beam = beam_list[j]  # token list (space-separated characters)
            score = score_list[j]  # tensor

            if untokenize_output:
                # Convert back from space-separated format to normal SMILES
                text = detokenize_smiles(beam)
            else:
                text = beam  # Keep as space-separated

            input_predictions.append(text)
            input_probs.append(10 ** score.item())  # convert log10 prob to prob
        
        predictions_by_input.append(input_predictions)
        probs_by_input.append(input_probs)

    return predictions_by_input, probs_by_input

def process_predictions_by_beam_rank(predictions_raw, scores_raw, beam_size, untokenize_output=True):
    """
    Process predictions to return results organized by beam rank (like reference code)
    Returns: predictions_by_beam, probs_by_beam
    Each is a list where index i contains all rank-i predictions across all inputs
    """
    predictions_by_beam = [[] for _ in range(beam_size)]
    probs_by_beam = [[] for _ in range(beam_size)]

    for i, (beam_list, score_list) in enumerate(zip(predictions_raw, scores_raw)):
        for j in range(min(beam_size, len(beam_list))):
            beam = beam_list[j]  # token list (space-separated characters)
            score = score_list[j]  # tensor

            if untokenize_output:
                # Convert back from space-separated format to normal SMILES
                text = detokenize_smiles(beam)
            else:
                text = beam  # Keep as space-separated

            predictions_by_beam[j].append(text)
            probs_by_beam[j].append(10 ** score.item())  # convert log10 prob to prob

    return predictions_by_beam, probs_by_beam

def predict_and_log(transformer, smiles_list,tokenize=True):
    tokenized_smiles = smiles_list
    if tokenize == True:
        # Convert SMILES to the format expected by the model
        tokenized_smiles = [tokenize_smiles(smiles) for smiles in smiles_list]
    
    # print(f"Original SMILES: {smiles_list}")
    # print(f"Tokenized for model: {tokenized_smiles}")
    
    scores, predictions = transformer.translate(src=tokenized_smiles, batch_size=64)
    
    preds, probs = process_predictions_by_beam_rank(predictions, scores, transformer.beam_size)
    
    # print(f"\n=== RESULTS ===")
    # print(f"# of inputs: {len(smiles_list)}")
    # print(f"# of predictions per input: {len(preds[0]) if preds else 0}")
    
    # # Print results for each input
    # for i, smiles in enumerate(smiles_list):
    #     print(f"\nInput {i+1}: {smiles}")
    #     for j, (pred, prob) in enumerate(zip(preds[i], probs[i])):
    #         print(f"  Prediction {j+1}: {pred} (prob: {prob:.6f})")

    # print("\n=== BEAM RANK ORGANIZATION ===")
    # for beam_rank in range(len(preds)):
    #     print(f"Beam {beam_rank + 1} predictions across all inputs:")
    #     for input_idx, pred in enumerate(preds[beam_rank]):
    #         print(f"  Input {input_idx + 1}: {pred}")

    return preds, probs


if __name__ == "__main__":
    model_path = "/home/<USER>/chemstack_retrosynthesis/assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt"
    transformer_T1 = load_onmt_transformer(model_path, beam_size=5)  

    test_smiles_list = [
        "Cc1cccc(C(=O)O)c1N",
        "CC(=O)Oc1ccccc1C(=O)O",
        "C1=CC=CN=C1"
    ]
    
    results, probs = predict_and_log(transformer_T1, test_smiles_list)
    
    # Now results[i] contains all 5 predictions for input i
    print(f"\nSummary:")
    for i, smiles in enumerate(test_smiles_list):
        print(f"Input: {smiles}")
        print(f"Top prediction: {results[i][0]} (prob: {probs[i][0]:.6f})")
        print()