import requests 
from config.settings import Config   
def get_reaction_class(rxn_string):
        if not isinstance(rxn_string, str) or not rxn_string.strip():
            return None
        
        # Add format validation
        if '>' not in rxn_string:
            print(f"Invalid reaction format (missing '>'): {rxn_string[:40]}...")
            return None
        
        try:
            response = requests.post(
                Config.REACTION_CLASS_URL,
                headers={'Content-Type': 'application/json'},
                json={"smiles": [rxn_string]}
            )
            data = response.json()
            
            if data['status'] == 'SUCCESS' and data['results']:
                # Extract only the Rank 1 result
                return next((r for r in data['results'] if r['rank'] == 1), None)
            return None
        except Exception as e:
            print(f"Error processing reaction class for {rxn_string[:20]}...: {e}")
            return None
    