from abc import ABC, abstractmethod
from scscore import SCScorer

class SynthesisScoreAPI(ABC):
    """Abstract API for getting synthesis scores."""
    
    @abstractmethod
    def get_synthesis_score(self, smiles: str) -> float:
        """Get synthesis score for a molecule."""
        pass

class SCScoreAPI(SynthesisScoreAPI):
    """Mock implementation for testing."""
    
    def get_synthesis_score(self, smiles: str) -> float:
        scs_scorer = SCScorer()
        return scs_scorer.get_score_from_smi(smiles)[1]  #((scs_scorer.get_score_from_smi(smiles)[1]-1)/4) #high is bad

