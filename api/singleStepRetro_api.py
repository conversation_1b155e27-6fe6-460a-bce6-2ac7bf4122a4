# retro_synthesis_mcts/api/retro_api.py
import pandas as pd

from api.reaction_classification_api import *
from api.trasformers_api import *
from core.single_step_retro.disconnections import *
from infra.db.disconnections_db import *
from infra.db.disconnections_fileStorage import *

class SingleStepRetroAPI:
    """API client for single-step retrosynthesis predictions."""
    
    def __init__(self,transformer_AutoTag,transformer_T1,transformer_T2, transformer_T3):
        """
        Initialize the retrosynthesis API.
        """    
        list_substructures_path= 'assets/stocks/list_conditionnal_substructures_tags_R2.csv'
        if list_substructures_path != '':
            with open(list_substructures_path, 'r') as f:
                reader = csv.reader(f)
                substructure_list = list(reader)   

        self.disconnection_tool = TopDisconnections(
            transformer_AutoTag,
            transformer_T1,
            transformer_T2, 
            transformer_T3,                   
            uspto_t1_path="assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt",
            uspto_t2_path="assets/mttl_models/USPTO_STEREO_separated_T2_Reagent_Pred_225000.pt",
            uspto_t3_path="assets/mttl_models/USPTO_STEREO_separated_T3_Forward_255000.pt",
            autotag_model_path="assets/mttl_models/USPTO_STEREO_separated_T0_AutoTag_260000.pt",
            substructure_list=substructure_list # Example substructures
        )  
    
    def get_retrosynthesis_reactions(self, target_smiles):
        """
        Get possible retrosynthesis reactions for a target molecule.
        
        Args:
            target_smiles (str): SMILES of the target molecule
            
        Returns:
            list: List of Reaction objects
        """
        reactions = []
        target_predictions = pd.DataFrame() 

        check_disconnections_db = 0
        if check_disconnections_db ==1:
            target_predictions = query_disconnections_target(self.canonicalize_smiles(target_smiles))
            target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
            print(f"Loaded {len(target_predictions)} records from db")   

        check_disconnections_fileStorage = 1
        if check_disconnections_fileStorage ==1:
            storage = DisconnectionsStorage()
            target_predictions = storage.load_disconnections(self.canonicalize_smiles(target_smiles))         
            print(f"Loaded {len(target_predictions)} records from fileStorage")  
            if len(target_predictions) >0:
                print(target_predictions.shape)
                #print(target_predictions.iloc[0])
                target_predictions= target_predictions[['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']]
                #target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
        
        #exit(1)
        if len(target_predictions) ==0 :
            print("No disconnections found in DB - creating from scratch")
            # Filter predictions for the target molecule
            target_predictions = self.disconnection_tool.get_potential_disconnections(target_smiles)    
            if target_predictions.empty:
                return reactions
            
            print(f"Created {target_predictions.shape} disconnections from scratch")
            print(target_predictions.iloc[0]) 
            #exit(1)        
            insert_disconnections_db = 0
            if insert_disconnections_db ==1:        
                success = insert_dataframe_to_postgres(target_predictions)
                if success:
                    print("Data inserted successfully!")
                else:
                    print("Failed to insert data")  

            insert_disconnections_fileStorage = 1
            if insert_disconnections_fileStorage ==1: 
                storage = DisconnectionsStorage()       
                success = storage.save_disconnections(target_predictions, self.canonicalize_smiles(target_smiles), analysis_id='test_analysis')
                print(f"Save successful: {success}")
                #exit(1)
                if success:
                    print("Data inserted successfully!")
                else:
                    print("Failed to insert data")                      
    
        else:
            print(f"Foudn {len(target_predictions)} disconnections in DB: returning")
        
        #remove invalid discconections by roundtrip logid
        # Keep predictions where T3 predicts the correct target, and target is not in the retro prediction:
        print(target_predictions.shape)
        target_predictions_Forw_val = target_predictions[target_predictions['Target'] == target_predictions['Forward_Prediction']]
        print(target_predictions_Forw_val.shape)
        target_predictions_Forw_val = target_predictions_Forw_val[target_predictions_Forw_val['Target'] != target_predictions_Forw_val['Retro']]
        print(target_predictions_Forw_val.shape)        

        target_predictions_Forw_val['rxn_string'] =target_predictions_Forw_val.apply(lambda s: s['Retro'] + '>' + s['Reagents'] + '>' + target_smiles,axis=1)
        target_predictions_Forw_val['rxn_class'] = target_predictions_Forw_val.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)
        print(target_predictions_Forw_val.shape) 
        print(target_predictions_Forw_val.iloc[0])

        #exit(1)
        return target_predictions_Forw_val

    def canonicalize_smiles(self, smiles: str) -> str:
        '''
        Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
        Also neutralizes any charge of the molecules.
        
        Args:
            smiles (str): SMILES string of the molecule(s).
        
        Returns:
            str: Canonicalized SMILES string of the molecule(s).
        '''
        returned = []
        any_error = False
        for molecule in smiles.split('.'):
            molecule = self.neutralize_smi(molecule)
            mol = Chem.MolFromSmiles(molecule)
            if mol is not None:
                returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
            else: 
                any_error = True
        if not any_error:
            return '.'.join(returned)
        else:
            return ''
        
    def neutralize_smi(self, smiles: str) -> str:        # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
        if '-' in smiles or '+' in smiles:
            try:
                mol = Chem.MolFromSmiles(smiles)
                pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
                at_matches = mol.GetSubstructMatches(pattern)
                at_matches_list = [y[0] for y in at_matches]
                if len(at_matches_list) > 0:
                    for at_idx in at_matches_list:
                        atom = mol.GetAtomWithIdx(at_idx)
                        chg = atom.GetFormalCharge()
                        hcount = atom.GetTotalNumHs()
                        atom.SetFormalCharge(0)
                        atom.SetNumExplicitHs(hcount - chg)
                        atom.UpdatePropertyCache()
                return Chem.MolToSmiles(mol)
            except:
                return smiles
        else:
            return smiles
