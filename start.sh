#!/bin/bash

# Start Celery worker in the background
celery -A main worker --loglevel=INFO --concurrency=1 --pool=prefork --without-gossip --without-mingle --without-heartbeat &

# Start Celery beat in the background
celery -A enrich_inflow_worker worker --loglevel=INFO --concurrency=1 --pool=prefork --without-gossip --without-mingle --without-heartbeat &

#
celery -A enrich_worker worker --loglevel=INFO --concurrency=5 --pool=prefork --without-gossip --without-mingle --without-heartbeat &

# Run health checks
python3 health_checks.py

# Keep script running
wait