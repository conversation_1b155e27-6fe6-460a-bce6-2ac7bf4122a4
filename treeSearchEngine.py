from typing import Optional 
from config.settings import Config
from api.synthesis_score_api import *
from core.tree.tree_nodes import *
from core.tree.scoring import *
from core.tree.tree_search import *
from core.validators.disconnection_validator import *
from core.validators.terminal_checker import *
from utils.common import *
from azure_utils.utils import AzureUtils
from utils.helpers import REQUEST_ID
from concurrent.futures import ThreadPoolExecutor, as_completed
from db.db_ops import DbOps
from celery.utils.log import get_task_logger
from enrich_inflow_worker import send_enrich_inflow_task


logger = get_task_logger("tasks")

class TreeSearchEngine:
    """Main engine for retro-synthesis tree search."""
    
    def __init__(
        self,
        single_step_api,
        synthesis_score_api: Optional[SynthesisScoreAPI] = None,
        config: Optional[Config] = None,
        kwargs: Optional[dict] = None
    ):
        self.config = config or Config()
        self.single_step_api = single_step_api
        self.synthesis_score_api = synthesis_score_api
        self.db_ops = DbOps()
        self.azure_utils = AzureUtils()
        self.kwargs = kwargs or {}
        
        # Initialize components
        self._setup_components()
        
        # Initialize tree search
        self.tree_search = RetroSynthesisTreeSearch(
            single_step_api=self.single_step_api,
            terminal_checker=self.terminal_checker,
            disconnection_validator=self.disconnection_validator,
            route_scorer=self.route_scorer,
            synthesis_score_api=self.synthesis_score_api,
            config=self.config
        )
    
    def _setup_components(self):
        """Setup all components with default configurations."""
        # Terminal checkers
        max_depth = self.kwargs.get('max_depth', self.config.MAX_DEPTH)
        depth_checker = DepthTerminalChecker(max_depth)
        score_checker = SynthesisScoreTerminalChecker(
            self.config.SYNTHESIS_SCORE_THRESHOLD,
            self.synthesis_score_api
        )
        bb_checker = BuildingBlockTerminalChecker(self.config.BUILDING_BLOCKS_PATH)
        self.terminal_checker = CompositeTerminalChecker([depth_checker, score_checker, bb_checker])
        
        # Disconnection validators
        prob_validator = ForwardProbabilityValidator(self.config.MIN_FORWARD_PROB)
        certainity_validator = ReactionCertaintyValidator(self.config.MIN_CERTAINITY_SCORE)
        heavyMetal_validator = HeavyMetalValidator(self.config.HEAVY_METAL_THRESHOLD)
        #precursor_validator =SimplerPrecursorValidator(self.config.HEAVY_METAL_THRESHOLD)

        #self.disconnection_validator = CompositeDisconnectionValidator([prob_validator, certainity_validator, heavyMetal_validator])#,precursor_validator])
        self.disconnection_validator = CompositeDisconnectionValidator([heavyMetal_validator, certainity_validator])

        
        # Route scorer
        self.route_scorer = LeafNodeSynthesisScoreRouteScorer(self.synthesis_score_api)
        #self.route_scorer = SynthesisScoreRouteScorer(self.synthesis_score_api)
    
    def find_synthesis_routes(
        self,
        target_smiles: str,
        max_routes: int = None,
        kwargs: Optional[dict] = None
    ):
        """
        Find the best synthesis routes for a target molecule.
        
        Args:
            target_smiles: SMILES of target molecule
            max_routes: Maximum number of routes to return
            
        Returns:
            List of synthesis routes
        """
        return self.tree_search.search(target_smiles, max_routes, kwargs=kwargs)

    def get_best_routes_from_checkpoint(self, checkpoint_id: str, max_routes: int = None) -> List[List[ReactionNode]]:
        """
        Load checkpoint and extract best routes without continuing search.
        
        Args:
            checkpoint_id: ID of the checkpoint to load
            max_routes: Maximum number of routes to return
            
        Returns:
            List of best synthesis routes from the checkpoint
        """
        import pickle
        
        if max_routes is None:
            max_routes = self.config.MAX_ROUTES
        
        # Load checkpoint data
        if self.config.USE_CELERY:
            import redis
            r = redis.from_url(self.config.REDIS_URL)
            checkpoint_data = pickle.loads(r.get(f"checkpoint:{checkpoint_id}"))
        else:
            with open(f"checkpoints/{checkpoint_id}.pkl", 'rb') as f:
                checkpoint_data = pickle.load(f)
        
        root = checkpoint_data['root_node']
        
        # Extract and score routes from current tree state
        routes = self._extract_routes(root)
        scored_routes = [(route, self.route_scorer.score_route(route)) for route in routes]
        
        # Sort by score and return top routes
        scored_routes.sort(key=lambda x: x[1])
        return [route for route, _ in scored_routes[:max_routes]]
    
    def _extract_routes(self, root: MoleculeNode) -> List[List[ReactionNode]]:
        """Extract all complete synthesis routes from the tree."""
        routes = []
        
        def dfs_extract(node: TreeNode, current_route: List[ReactionNode]) -> None:
            if isinstance(node, MoleculeNode):
                if node.status == NodeStatus.TERMINAL:
                    # Found a complete route
                    if current_route:
                        routes.append(current_route.copy())
                else:
                    # Continue with solved reactions
                    for reaction in node.get_solved_reactions():
                        dfs_extract(reaction, current_route)
            
            elif isinstance(node, ReactionNode):
                if node.is_solved():
                    current_route.append(node)
                    # Process all reactants
                    for reactant in node.get_reactant_nodes():
                        dfs_extract(reactant, current_route)
                    current_route.pop()
        
        dfs_extract(root, [])
        return routes
    
    def store_pathways(self, target_smiles: str, routes: List[List[ReactionNode]], storage_path: str = "pathways", kwargs : dict = {}) -> dict:
        """
        Store extracted synthesis pathways to local storage for later processing.
        
        Args:
            target_smiles: Target molecule SMILES
            routes: List of synthesis routes to store
            storage_path: Directory path for storage
            
        Returns:
            Filepath of stored pathways file
        """
        import os
        import json
        import time
        
        # Create storage directory
        os.makedirs(storage_path, exist_ok=True)
        
        # Create filename with timestamp
        timestamp = int(time.time())
        safe_smiles = target_smiles.replace('/', '_').replace('\\', '_')
        depth = kwargs.get('max_depth', self.config.MAX_DEPTH)
        beam_width = kwargs.get('beam_width', self.config.BEAM_WIDTH)

        # Convert routes to serializable format
        pathways_data = {
            'target_smiles': target_smiles,
            'timestamp': timestamp,
            'readable_time': time.ctime(),
            'num_routes': len(routes),
            'routes': []
        }


        smiles_metadata = {}
        all_data = []
        logger.info(f"Storing final {len(routes)} routes for target: {target_smiles} with max depth: {depth} and beam width: {beam_width}")
        for route_idx, route in enumerate(routes):
            route_data = {
                'route_id': route_idx + 1,
                'num_steps': len(route),
                'unique_id' : hash(str(route) + str(depth) + str(beam_width)),
                'reactions': []
            }
            raw_smiles = []
            route_level_image = ''
            route_name = ''
            for step_idx, reaction in enumerate(route):
                reaction_data = {
                    'step': step_idx + 1,
                    'reaction_id': reaction.node_id,
                    'reaction_string': reaction.reaction_data.get('rxn_string', 'N/A'),
                    'retro_smiles': reaction.reaction_data.get('Retro', ''),
                    'reagents': reaction.reaction_data.get('Reagents', ''),
                    'forward_prediction': reaction.reaction_data.get('Forward_Prediction', ''),
                    'prob_forward_1': reaction.reaction_data.get('Prob_Forward_Prediction_1', 0.0),
                    'prob_forward_2': reaction.reaction_data.get('Prob_Forward_Prediction_2', 0.0),
                    'score': reaction.reaction_score,# reaction.reaction_data.get('Score', 0.0),
                    'rxn_class': reaction.reaction_data.get('rxn_class', 0.0),
                    'reaction_smiles_img' :  self.azure_utils.image_to_blob(
                        reaction.reaction_data.get('rxn_string', '')),
                    'other_information' : smiles_metadata.get(
                        reaction.reaction_data.get('rxn_string', ''), {}),
                    'reactants': []
                }
                if not route_level_image:
                    route_level_image = reaction_data.get('reaction_smiles_img', '')
                
                if not route_name:
                    route_name = reaction_data.get('rxn_class', {}).get('reaction_name')

                mid_materials = []
                for reactant_node in reaction.get_reactant_nodes():
                    reactant_info = {
                        'smiles': reactant_node.smiles,
                        'name' : smiles_to_iupac_pubchem(reactant_node.smiles),
                        'synthesis_score': reactant_node.synthesis_score,
                        'is_terminal': reactant_node.status.value if hasattr(reactant_node.status, 'value') else str(reactant_node.status)
                    }
                    reaction_data['reactants'].append(reactant_info)
                    mid_materials.append(reactant_info.get('name'))
                
                route_data['reactions'].append(reaction_data)
                raw_smiles = mid_materials
            
            # Calculate total route score
            # total_score = sum(
            #     reactant.synthesis_score or 0 
            #     for reaction in route 
            #     for reactant in reaction.get_reactant_nodes()
            # )
            # route_data['total_route_score'] = total_score
            route_data['total_route_score'] =1-((self.route_scorer.score_route(route) -1)/4)
            # total_score = 0
            # for reaction in route:
            #     max_reactant_score_logic = 0
            #     if max_reactant_score_logic ==1:
            #         reactant_scores = [
            #             reactant.synthesis_score or 0 
            #             for reactant in reaction.get_reactant_nodes()
            #         ]
            #         if reactant_scores:
            #             total_score += max(reactant_scores)
            #     else:
            #         total_score += -reaction.reaction_score


            logger.info(f"Storing route {route_idx + 1} with {len(route)} steps, total score: {route_data['total_route_score']}")
            route_id = self.db_ops.insert_retro_data(
                target_smiles=target_smiles,
                request_id=kwargs.get('request_id', REQUEST_ID.get(None)),
                unique_id=route_data['unique_id'],
                route_id=route_data['route_id'],    
                num_steps=route_data['num_steps'],
                raw_smiles=raw_smiles,
                total_cost = 0,
                total_route_score=route_data['total_route_score'],
                data=route_data['reactions'],
                route_name=route_name,
                route_level_image=route_level_image,
                config={
                    'max_depth': kwargs.get('max_depth', self.config.MAX_DEPTH),
                    'beam_width': kwargs.get('beam_width', self.config.BEAM_WIDTH)
                }
                )
            if route_id:
                logger.info(f"Sending enrich task for route_id: {route_id}")
                other_info = []
                for dt in route_data['reactions']:
                    other_info.append({'reaction_smiles' : dt.get('reaction_string'), 'reaction_class' : json.dumps(dt.get('rxn_class'))})
                all_data.append({"route_id": route_id, 'other_info' : other_info , 'total_score' : route_data['total_route_score']})
            logger.info(f"Stored route {route_data['route_id']} with {route_data['num_steps']} steps and score {route_data['total_route_score']}")
            pathways_data['routes'].append(route_data)
        all_data = sorted(all_data, key=lambda x: x['total_score'], reverse=True)
        for dt in all_data[:25]:
            send_enrich_inflow_task({"route_id": dt['route_id'], 'other_info' : dt['other_info']})
            # route_data['total_route_score'] = total_score            
            
            
        
        # # Write to JSON file
        # with open(filepath, 'w') as f:
        #     json.dump(pathways_data, f, indent=2)
        
        # print(f"Stored {len(routes)} pathways to: {filepath}")
        return pathways_data
        