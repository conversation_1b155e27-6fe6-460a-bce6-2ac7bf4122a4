#!/usr/bin/env python3
"""
Test script to verify parallel tree expansion functionality
"""

import os
import sys
import time
import logging
from unittest.mock import Mock, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def test_parallel_vs_sequential():
    """Test parallel tree expansion vs sequential"""
    try:
        # Import after setting up path
        from config.settings import Config
        from core.tree.tree_nodes import MoleculeNode
        from treeSearchEngine import TreeSearchEngine
        
        # Mock the APIs to avoid external dependencies
        mock_single_step_api = Mock()
        mock_synthesis_score_api = Mock()
        
        # Mock disconnections data
        import pandas as pd
        mock_disconnections = pd.DataFrame({
            'Retro': ['CC.O', 'CCC.N', 'CCCC.S'],
            'Prob_Forward_Prediction_1': [0.8, 0.7, 0.6],
            'other_field': ['val1', 'val2', 'val3']
        })
        
        mock_single_step_api.get_retrosynthesis_reactions.return_value = mock_disconnections
        mock_synthesis_score_api.get_synthesis_score.return_value = 2.5
        
        # Create config
        config = Config()
        
        # Test molecule
        target_smiles = "O=C(O)C1=CC(Cl)=CC(=C1N)C"
        
        # Test parameters
        test_kwargs = {
            'max_depth': 2,
            'beam_width': 3,
            'checkpoint_frequency': 1000,
            'max_workers': 2
        }
        
        print("🧪 Testing Tree Search Engine with Parallel Processing")
        print(f"Target SMILES: {target_smiles}")
        print(f"Test parameters: {test_kwargs}")
        
        # Test 1: Sequential processing
        print("\n📊 Test 1: Sequential Tree Expansion")
        engine_seq = TreeSearchEngine(
            single_step_api=mock_single_step_api,
            synthesis_score_api=mock_synthesis_score_api,
            config=config
        )
        
        start_time = time.time()
        test_kwargs_seq = test_kwargs.copy()
        test_kwargs_seq['use_parallel'] = False
        
        routes_seq, msg_seq = engine_seq.find_synthesis_routes(
            target_smiles=target_smiles,
            max_routes=10,
            kwargs=test_kwargs_seq
        )
        seq_time = time.time() - start_time
        
        print(f"✅ Sequential completed in {seq_time:.2f}s")
        print(f"   Found {len(routes_seq)} routes")
        
        # Test 2: Parallel processing
        print("\n⚡ Test 2: Parallel Tree Expansion")
        engine_par = TreeSearchEngine(
            single_step_api=mock_single_step_api,
            synthesis_score_api=mock_synthesis_score_api,
            config=config
        )
        
        start_time = time.time()
        test_kwargs_par = test_kwargs.copy()
        test_kwargs_par['use_parallel'] = True
        
        routes_par, msg_par = engine_par.find_synthesis_routes(
            target_smiles=target_smiles,
            max_routes=10,
            kwargs=test_kwargs_par
        )
        par_time = time.time() - start_time
        
        print(f"✅ Parallel completed in {par_time:.2f}s")
        print(f"   Found {len(routes_par)} routes")
        
        # Compare results
        print(f"\n📈 Performance Comparison:")
        print(f"   Sequential: {seq_time:.2f}s, {len(routes_seq)} routes")
        print(f"   Parallel:   {par_time:.2f}s, {len(routes_par)} routes")
        
        if par_time < seq_time:
            speedup = seq_time / par_time
            print(f"   🚀 Parallel is {speedup:.2f}x faster!")
        else:
            slowdown = par_time / seq_time
            print(f"   🐌 Parallel is {slowdown:.2f}x slower (overhead for small trees)")
        
        # Verify both methods found routes
        assert len(routes_seq) > 0, "Sequential should find routes"
        assert len(routes_par) > 0, "Parallel should find routes"
        
        print(f"\n✅ Both methods successfully found synthesis routes!")
        print(f"   Sequential routes: {len(routes_seq)}")
        print(f"   Parallel routes: {len(routes_par)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parallel_configuration():
    """Test different parallel configurations"""
    try:
        print("\n🔧 Testing Different Parallel Configurations")
        
        # Import after setting up path
        from config.settings import Config
        from treeSearchEngine import TreeSearchEngine
        
        # Mock the APIs
        mock_single_step_api = Mock()
        mock_synthesis_score_api = Mock()
        
        import pandas as pd
        mock_disconnections = pd.DataFrame({
            'Retro': ['CC.O', 'CCC.N', 'CCCC.S', 'CCCCC.P'],
            'Prob_Forward_Prediction_1': [0.8, 0.7, 0.6, 0.5],
            'other_field': ['val1', 'val2', 'val3', 'val4']
        })
        
        mock_single_step_api.get_retrosynthesis_reactions.return_value = mock_disconnections
        mock_synthesis_score_api.get_synthesis_score.return_value = 2.5
        
        config = Config()
        target_smiles = "O=C(O)C1=CC(Cl)=CC(=C1N)C"
        
        # Test different worker counts
        worker_counts = [1, 2, 4]
        
        for workers in worker_counts:
            print(f"\n   Testing with {workers} workers...")
            
            engine = TreeSearchEngine(
                single_step_api=mock_single_step_api,
                synthesis_score_api=mock_synthesis_score_api,
                config=config
            )
            
            test_kwargs = {
                'max_depth': 2,
                'beam_width': 4,
                'use_parallel': True,
                'max_workers': workers
            }
            
            start_time = time.time()
            routes, msg = engine.find_synthesis_routes(
                target_smiles=target_smiles,
                max_routes=10,
                kwargs=test_kwargs
            )
            elapsed = time.time() - start_time
            
            print(f"     {workers} workers: {elapsed:.2f}s, {len(routes)} routes")
        
        print("✅ All parallel configurations tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Starting Parallel Tree Expansion Tests")
    
    success = True
    
    # Test 1: Basic parallel vs sequential
    if not test_parallel_vs_sequential():
        success = False
    
    # Test 2: Different configurations
    if not test_parallel_configuration():
        success = False
    
    if success:
        print("\n🎉 All tests passed! Parallel tree expansion is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the implementation.")
        sys.exit(1)
