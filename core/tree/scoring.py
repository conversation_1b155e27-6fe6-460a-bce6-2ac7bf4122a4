from abc import ABC, abstractmethod
from typing import List, Set
import numpy as np
from core.tree.tree_nodes import *

class RouteScorer(ABC):
    """Abstract base class for route scoring strategies."""
    
    @abstractmethod
    def score_route(self, route: List[ReactionNode]) -> float:
        """Calculate score for a complete synthesis route."""
        pass

class SynthesisScoreRouteScorer(RouteScorer):
    """Score routes based on sum of reactant synthesis scores."""
    
    def __init__(self, synthesis_score_api):
        self.synthesis_score_api = synthesis_score_api
    
    def score_route(self, route: List[ReactionNode]) -> float:
        """Score based on total synthesis score of all reactants."""
        total_score = 0.0
        
        for reaction in route:
            max_reactant_score_rxn = 0.0
            #print("*********")
            #print(reaction.get_reactant_nodes())
            #print("*********")
            for reactant_node in reaction.get_reactant_nodes():
                if reactant_node.synthesis_score is None:
                    reactant_node.synthesis_score = self.synthesis_score_api.get_synthesis_score(
                        reactant_node.smiles
                    )
                    if reactant_node.synthesis_score > max_reactant_score_rxn:
                        max_reactant_score_rxn = reactant_node.synthesis_score
            total_score += max_reactant_score_rxn # adding up all max-reactant-score
            #print(f"{total_score}")
        #exit(1)
        return total_score #will prefer routes which have lower total (max -reactnat score)

class LeafNodeSynthesisScoreRouteScorer(RouteScorer):
    """
    Advanced route scorer that focuses on leaf nodes only.
    Calculates the sum of synthesis scores for all reactant nodes 
    that are leaf nodes (terminal nodes with no further reactions).
    """
    
    def __init__(self, synthesis_score_api):
        self.synthesis_score_api = synthesis_score_api
    
    def score_route(self, route: List[ReactionNode]) -> float:
        """
        Score based on sum of synthesis scores of leaf node reactants only.
        
        Leaf nodes represent the actual starting materials that need to be 
        purchased/obtained, making this a more realistic cost assessment.
        """
        # Build the complete tree structure to identify leaf nodes
        leaf_reactants = self._identify_leaf_reactants(route)
        #print(len(leaf_reactants))
        if len(leaf_reactants) >0:
            #print(leaf_reactants)
            max_score = np.max(leaf_reactants)
            total_score = np.sum(leaf_reactants)        
            #print(f"max reactant score: {max_score} -- total reactant score: {total_score}")  
            return max_score
        else:
            return 100
    
    def _identify_leaf_reactants(self, route: List[ReactionNode]): #-> Set[MoleculeNode]:
        """
        Identify all reactant nodes that are leaf nodes (not produced by any reaction).
        
        A leaf node is a reactant that doesn't appear as a product in any reaction
        in the route - these represent the actual starting materials.
        """
        # Collect all products (molecules that are synthesized)
        products = set()
        for reaction in route:
            products.add(reaction.reaction_data.get('Target', ''))
        #print(f"+++products = {products}")
        # Collect all reactants that are NOT products (i.e., leaf nodes)
        leaf_reactants = [] #set()
        for reaction in route:
            for reactant_node in reaction.get_reactant_nodes():
                #print(reactant_node.smiles)
                #exit(1)
                # If this reactant is not produced by any reaction, it's a leaf
                if reactant_node.smiles not in products:
                    #leaf_reactants.add(reactant_node.smiles)#synthesis_score
                    #print(reactant_node.synthesis_score)
                    leaf_reactants.append(reactant_node.synthesis_score)#synthesis_score
        
        return leaf_reactants

class WeightedLeafNodeRouteScorer(RouteScorer):
    """
    Enhanced version with additional weighting factors for more sophisticated scoring.
    """
    
    def __init__(self, synthesis_score_api, complexity_weight=1.0, availability_weight=0.5):
        self.synthesis_score_api = synthesis_score_api
        self.complexity_weight = complexity_weight
        self.availability_weight = availability_weight
    
    def score_route(self, route: List[ReactionNode]) -> float:
        """
        Advanced scoring combining synthesis scores with route complexity metrics.
        """
        leaf_reactants = self._identify_leaf_reactants(route)
        
        # Base score from leaf node synthesis scores
        synthesis_score = 0.0
        for reactant_node in leaf_reactants:
            if reactant_node.synthesis_score is None:
                reactant_node.synthesis_score = self.synthesis_score_api.get_synthesis_score(
                    reactant_node.smiles
                )
            synthesis_score += reactant_node.synthesis_score
        
        # Additional complexity penalties
        route_length_penalty = len(route) * 0.1  # Prefer shorter routes
        leaf_count_penalty = len(leaf_reactants) * 0.05  # Prefer fewer starting materials
        
        total_score = (
            synthesis_score * self.complexity_weight + 
            route_length_penalty + 
            leaf_count_penalty
        )
        
        print(f"Synthesis score: {synthesis_score}")
        print(f"Route length penalty: {route_length_penalty}")
        print(f"Leaf count penalty: {leaf_count_penalty}")
        print(f"Total weighted score: {total_score}")
        
        return total_score
    
    def _identify_leaf_reactants(self, route: List[ReactionNode]) -> Set[MoleculeNode]:
        """Same leaf identification logic as the base class."""
        products = set()
        for reaction in route:
            products.add(reaction.get_product_node().smiles)
        
        leaf_reactants = set()
        for reaction in route:
            for reactant_node in reaction.get_reactant_nodes():
                if reactant_node.smiles not in products:
                    leaf_reactants.add(reactant_node)
                    print(reactant_node.synthesis_score)
                    exit(1)
        
        return leaf_reactants

'''
scored_routes.sort(key=lambda x: x[1]) sorts in ascending order (lowest to highest).
This means:
Routes with lower scores will be at the beginning of the list
When we take [:max_routes], we're selecting the routes with the lowest scores
The algorithm prefers routes with lower total/max synthesis scores

So the system is designed to pick routes where the sum of all reactant synthesis scores is minimized - 
which aligns with the expectation that lower synthesis scores = easier to synthesize.
'''