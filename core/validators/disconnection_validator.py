from abc import ABC, abstractmethod
import pandas as pd
from typing import List
from utils.disconnection_utils import *
from scscore import SCScorer

class DisconnectionValidator(ABC):
    """Abstract base class for disconnection validators."""
    
    @abstractmethod
    def is_valid(self, disconnection_row: pd.Series, target_smiles) -> bool:
        """Check if a disconnection is valid."""
        pass

class ForwardProbabilityValidator(DisconnectionValidator):
    """Validate disconnections based on forward prediction probability."""
    
    def __init__(self, min_prob: float):
        self.min_prob = min_prob
    
    def is_valid(self, disconnection_row: pd.Series,target_smiles) -> bool:
        return disconnection_row.get('Prob_Forward_Prediction_1', 0.0) >= self.min_prob

class ReactionCertaintyValidator(DisconnectionValidator):
    """Validate disconnections based on forward prediction probability."""
    
    def __init__(self, min_prob: float):
        self.min_prob = min_prob

    def is_valid(self, disconnection_row: pd.Series, target_smiles) -> bool:
        if disconnection_row['rxn_class'] is None or disconnection_row['rxn_class'].get('rank') != 1:
            return True      
        else:
            # print(self.min_prob)
            # print(disconnection_row['rxn_class'].get('prediction_certainty', 0.0))
            # exit(1)
            return disconnection_row['rxn_class'].get('prediction_certainty', 0.0) >= self.min_prob

class HeavyMetalValidator(DisconnectionValidator):
    """Validate disconnections based on forward prediction probability."""
    
    def __init__(self, min_prob: float):
        self.min_prob = min_prob

    def is_valid(self, disconnection_row: pd.Series, target_smiles) -> bool:
        return is_clean_reagent(disconnection_row)
    
class SimplerPrecursorValidator(DisconnectionValidator):
    """Validate disconnections based on forward prediction probability."""
    
    def __init__(self, min_prob: float):
        self.min_prob = min_prob

    def is_valid(self, disconnection_row: pd.Series, target_smiles) -> bool:        
        max_reactant_score = get_max_reactant_score(disconnection_row['Retro'])
        target_score = scs_scorer.get_score_from_smi(target_smiles)[1]
        threshold = 1 - ((target_score - 1) / 4)

        precursor_buffer = 0
        if max_reactant_score < threshold + precursor_buffer:
            return True

class CompositeDisconnectionValidator(DisconnectionValidator):
    """Composite validator that combines multiple validation criteria."""
    
    def __init__(self, validators: List[DisconnectionValidator]):
        self.validators = validators
    
    def is_valid(self, disconnection_row: pd.Series, target_smiles) -> bool:
        return all(validator.is_valid(disconnection_row,target_smiles) for validator in self.validators)
