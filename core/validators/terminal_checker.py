from abc import ABC, abstractmethod
from typing import Protocol, List, Dict, Any
import json
from core.tree.tree_nodes import *

class <PERSON><PERSON><PERSON><PERSON>(ABC):
    """Abstract base class for terminal condition checkers."""
    
    @abstractmethod
    def is_terminal(self, node: MoleculeNode) -> bool:
        """Check if a molecule node should be considered terminal."""
        pass

class De<PERSON>h<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(TerminalChecker):
    """Check if node is terminal based on depth."""
    
    def __init__(self, max_depth: int):
        self.max_depth = max_depth
    
    def is_terminal(self, node: MoleculeNode) -> bool:
        return node.depth >= self.max_depth

class SynthesisScoreTerminal<PERSON>hecker(TerminalChecker):
    """Check if node is terminal based on synthesis score."""
    
    def __init__(self, threshold: float, synthesis_score_api):
        self.threshold = threshold
        self.synthesis_score_api = synthesis_score_api
    
    def is_terminal(self, node: MoleculeNode) -> bool:
        if node.synthesis_score is None:
            node.synthesis_score = self.synthesis_score_api.get_synthesis_score(node.smiles)
        return node.synthesis_score < self.threshold
    def is_terminal_smiles(self, synthesis_score: float) -> bool:
        return synthesis_score < self.threshold

class BuildingBlockTerminalChecker(TerminalChecker):
    """Check if node is terminal based on whether it exists in building blocks JSON."""
    
    def __init__(self, building_blocks_path: str):
        """
        Initialize with path to building blocks JSON file.
        
        Args:
            building_blocks_path: Path to the buildingblocks.json file
        """
        self.building_blocks_path = building_blocks_path
        self.building_blocks = self._load_building_blocks()
    
    def _load_building_blocks(self) -> Dict[str, Any]:
        """Load building blocks from JSON file."""
        try:
            with open(self.building_blocks_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Building blocks file not found: {self.building_blocks_path}")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON format in building blocks file: {self.building_blocks_path}")
    
    def is_terminal(self, node: MoleculeNode) -> bool:
        """
        Check if the molecule node corresponds to a building block.
        
        Returns True if the node's SMILES matches any building block SMILES in the JSON.
        """
        node_smiles = node.smiles
        
        # Check if the SMILES matches any building block
        for block_name, block_data in self.building_blocks.items():
            if block_data.get("smiles") == node_smiles:
                return True
        
        return False
    
    def get_building_block_info(self, node: MoleculeNode) -> Dict[str, Any]:
        """
        Get building block information for a terminal node.
        
        Returns:
            Dictionary containing building block info or None if not found
        """
        node_smiles = node.smiles
        
        for block_name, block_data in self.building_blocks.items():
            if block_data.get("smiles") == node_smiles:
                return {
                    "name": block_name,
                    "smiles": block_data.get("smiles"),
                    "prices": block_data.get("prices", []),
                    "sources": block_data.get("sources", [])
                }
        
        return None


class CompositeTerminalChecker(TerminalChecker):
    """Composite checker that combines multiple terminal conditions."""
    
    def __init__(self, checkers: List[TerminalChecker]):
        self.checkers = checkers
    
    def is_terminal(self, node: MoleculeNode) -> bool:
        return any(checker.is_terminal(node) for checker in self.checkers)