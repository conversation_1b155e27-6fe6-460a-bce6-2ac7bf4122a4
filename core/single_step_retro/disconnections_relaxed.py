import sys
import os
import pandas as pd
import argparse
from os.path import exists

import requests
import numpy as np
import pandas as pd
import datetime
import os
from tqdm import tqdm
from rdkit import Chem
import pandas as pd
import random
import csv 

from core.single_step_retro.singleStep_retro_relaxed import SingleStepRetrosynthesisMultiModelsRelaxed
from api.trasformers_api import *
from core.rxnmarkcenter import RXNMarkCenter
from config.singlestep_config_yaml import SingleStepConfig

from scscore import SCScorer

# Default values for MCTS
#populate default values in case ConfigFile is missing keys
default_values = {
    'project_name': '',
    'mark_count': 6,#3,
    'neighbors': True,
    'Random_Tagging': True,
    'AutoTagging': True,
    'AutoTagging_Beam_Size': 100,#50,
    'Substructure_Tagging': True,
    'Retro_ENZR': False,
    'Retro_USPTO': True,
    'Fwd_ENZ_Reag_Pred': False,
    'Fwd_USPTO_Reag_Pred': True,
    'USPTO_Reag_Beam_Size': 1, #3
    'similarity_filter': False,
    'confidence_filter': False,
    'Retro_beam_size': 10,#5,
    'mark_locations_filter': 1.0,
    'Commercial_exclusions': [],
    'list_substructures_path': '',
    'list_substr_path_ENZR': '',
    'log': True,
    'Model_Folder': '',
    'USPTO_AutoTag_path': '',
    'USPTO_T1_path': '', 
    'USPTO_T2_path': '',
    'USPTO_T3_path': '',
    'ENZR_AutoTag_path': '',
    'ENZR_T1_path': '', 
    'ENZR_T2_path': '',
    'ENZR_T3_path': '',
    'ENZR_confidence_threshold': 0.9,
    'tmp_file_path': 'output/',
    'commercial_file_path': 'assets/stocks/dw_Commercial_canonical.smi',
    'max_iterations': 15
}

#terminal_mols={}

def load_config(configfile):

    if exists(configfile):
        conf_dict = SingleStepConfig(configfile)
    elif exists(configfile + '.yaml'):
        conf_dict = SingleStepConfig(configfile + '.yaml')
    else:
        print(f'Cannot find input config file: {configfile}')
        sys.exit(2)

    if not exists(conf_dict.commercial_file_path):
        print(f'Cannot find commercial molecules file: {conf_dict.commercial_file_path}')
        sys.exit(2)

    # if not hasattr(conf_dict, 'target_cpd') or conf_dict.target_cpd == '':
    #     print('No target compound specified in config.')
    #     sys.exit(2)

    #populate default values in case ConfigFile is missing keys
    for key, value in default_values.items():
        if not hasattr(conf_dict, key):
            setattr(conf_dict, key, value)

    if conf_dict.project_name == '':
        conf_dict.project_name = os.path.splitext(os.path.basename(configfile))[0]

    return conf_dict

conf = load_config('config/singlestep_config.yaml')
print(conf)
#exit(1)
with open(conf.commercial_file_path, 'r') as f:
    commercial_smis = f.read().splitlines()


scs_scorer = SCScorer()

class TopDisconnectionsRelaxed:
    """
    Utility for predicting top retrosynthetic disconnections for a given product molecule.
    Incorporates advanced reaction center tagging using:
    - Random Tagging
    - AutoTagging (Transformer model-based)
    - Substructure Matching
    """
    def __init__(self, transformer_autoTag, transformer_t1,transformer_t2, transformer_t3,                     
                 uspto_t1_path, uspto_t2_path, uspto_t3_path, autotag_model_path, 
                 substructure_list, mark_count=3, neighbors=True, 
                 random_tagging=True, auto_tagging=True, substructure_tagging=True, 
                 retro_enzr=False, retro_uspto=True, fwd_enz_reag_pred=False, 
                 fwd_uspto_reag_pred=True, uspto_reag_beam_size=3, 
                 similarity_filter=False, confidence_filter=False, retro_beam_size=5, 
                 mark_locations_filter=1.0, log=True):
        """
        Initialize the disconnection utility with pretrained models and substructures.
        """
        self.scscore = SCScorer()
        self.rxn_mark_center = RXNMarkCenter()
        self.retro_model = SingleStepRetrosynthesisMultiModelsRelaxed(
            # transformer_AutoTag = transformer_autoTag, 
            # transformer_T1 = transformer_t1,
            # transformer_T2 = transformer_t2, 
            # transformer_T3 = transformer_t3, 
            USPTO_T1_path=uspto_t1_path,
            USPTO_T2_path=uspto_t2_path,
            USPTO_T3_path=uspto_t3_path,
            list_substructures = substructure_list,
            USPTO_AutoTag_path = autotag_model_path
        )
        self.autotag_model_path = autotag_model_path  # Transformer model for tagging
        self.list_substructures = substructure_list   # Predefined substructures for tagging
        self.mark_count = mark_count
        self.neighbors = neighbors
        self.Random_Tagging = random_tagging
        self.AutoTagging = auto_tagging
        self.Substructure_Tagging = substructure_tagging
        self.Retro_ENZR = retro_enzr
        self.Retro_USPTO = retro_uspto
        self.Fwd_ENZ_Reag_Pred = fwd_enz_reag_pred
        self.Fwd_USPTO_Reag_Pred = fwd_uspto_reag_pred
        self.USPTO_Reag_Beam_Size = uspto_reag_beam_size
        self.similarity_filter = similarity_filter
        self.confidence_filter = confidence_filter
        self.Retro_beam_size = retro_beam_size
        self.mark_locations_filter = mark_locations_filter
        self.log = log


    def get_potential_disconnections(self, product_smiles):
        print(f"🔍 Processing Product: {product_smiles}")

        # Step 1: Canonicalize SMILES
        product_smiles = self.retro_model.canonicalize_smiles(product_smiles)

        # Step 2: Predict retrosynthetic disconnections using Execute_Retro_Prediction
        predictions_df=pd.DataFrame()
        predictions_df, _ = self.retro_model.Execute_Retro_Prediction(
            SMILES = product_smiles, 
            mark_count = self.mark_count,
            neighbors = self.neighbors,
            Random_Tagging = self.Random_Tagging,
            AutoTagging = self.AutoTagging, 
            Substructure_Tagging = self.Substructure_Tagging,
            Retro_ENZR = self.Retro_ENZR,
            Retro_USPTO = self.Retro_USPTO, 
            Fwd_ENZ_Reag_Pred = self.Fwd_ENZ_Reag_Pred, 
            Fwd_USPTO_Reag_Pred = self.Fwd_USPTO_Reag_Pred, 
            USPTO_Reag_Beam_Size = self.USPTO_Reag_Beam_Size, 
            similarity_filter = self.similarity_filter,
            confidence_filter = self.confidence_filter,
            Retro_beam_size = self.Retro_beam_size,
            mark_locations_filter = self.mark_locations_filter,
            log = self.log
        )
        print("*********")
        print("+++++++")
        print(predictions_df.head())
        print(predictions_df.shape)
        # print(predictions_df.columns)
        #exit(1)
        #predictions_df.to_csv("predictions_df_0.csv")

        if predictions_df.empty:
            print("❌ No valid disconnections found.")
            return predictions_df # None - will check for empt
        else:
            #predictions_df['Score'] = predictions_df.apply(lambda s: 10 - scs_scorer.get_score_from_smi(s['Retro'])[1], axis=1)
            #predictions_df['Score'] = predictions_df.apply(lambda s: 1-((scs_scorer.get_score_from_smi(s['Retro'])[1]-1)/4), axis=1)
            # def max_score_retro(retro_list):
            #     retro_molecules = retro_list.split('.')
            #     max_score = -1
            #     for mol in retro_molecules:
            #         temp_score = ((scs_scorer.get_score_from_smi(mol)[1]-1)/4) #high is bad
            #         if temp_score > max_score:
            #             max_score = temp_score
            #     return 1-max_score #low is bad -we want simple reactants to be ranked higher
            # predictions_df['Score']=predictions_df.apply(lambda s:max_score_retro(s['Retro']),axis=1)
            
            return predictions_df
    
