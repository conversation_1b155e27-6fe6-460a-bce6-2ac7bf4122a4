import pandas as pd
import json
import requests
import re
from config.settings import Config

def predict_retrosynthesis_askscos_AT(smiles, api_url=Config.ASKOS_PREDICTIONS_PISTACHO_URL):
    """
    Make an API call to get retrosynthesis predictions
    
    Parameters:
    -----------
    smiles : str or list
        The SMILES string(s) to predict retrosynthesis for
    api_url : str
        The URL of the API endpoint
    
    Returns:
    --------
    dict
        The API response as a dictionary
    """
    # If smiles is a string, convert it to a list
    if isinstance(smiles, str):
        smiles = [smiles]
    
    # Prepare the request
    headers = {"Content-Type": "application/json"}
    data = {"smiles": smiles}
    
    # Make the request
    response = requests.post(api_url, headers=headers, json=data)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"API request failed with status code {response.status_code}: {response.text}")

def create_tagged_target_AT(smiles):
    """
    Create a tagged version of the target SMILES
    This is a simplified example - you may need a more sophisticated tagging algorithm
    
    Parameters:
    -----------
    smiles : str
        The SMILES string to tag
    
    Returns:
    --------
    str
        The tagged SMILES string
    """
    # This is a simplified tagging example - replace with your actual tagging logic
    # Here we're just adding '!' to carbon and oxygen atoms in functional groups
    tagged = smiles.replace("C(=O)O", "C(=O)O!")
    tagged = tagged.replace("Cc", "C!c!")
    
    return tagged

def create_dataframe_from_api_response_AT(api_response, target_smiles, tagged_target, model_path="/home/<USER>/multiStep_retroSynthesis/assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt"):
    """
    Convert API response to a DataFrame with the specified columns.
    
    Parameters:
    -----------
    api_response : dict
        The API response as a dictionary
    target_smiles : str
        The original SMILES input
    model_path : str, optional
        The path to the model to use as T1_Model value
        
    Returns:
    --------
    pandas.DataFrame
        A DataFrame with the required columns
    """
    # Extract products and scores from the first result
    products = api_response[0]['products']
    scores = api_response[0]['scores']
    
    # Create a tagged version of the target
    #tagged_target = create_tagged_target(target_smiles)
    
    # Create rows for the DataFrame
    rows = []
    for i, (product, score) in enumerate(zip(products, scores)):
        row = {
            'T1_Model': model_path,
            'ID': f"R_1.{i+1}",
            'ID_Tag': 1,
            'ID_beam': i+1,
            'Target': target_smiles,
            'Tagged_Target': tagged_target,
            'Retro': product,
            'Retro_Conf': score
        }
        rows.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(rows)
    
    return df

def main():
    # Target SMILES
    target_smiles = "Cc1cc(Cl)cc(C(=O)O)c1N"
    
    # Make API call
    try:
        api_response = predict_retrosynthesis_askscos_AT(target_smiles)

        tagged_target = create_tagged_target_AT(target_smiles)
        
        # Create DataFrame from API response
        df = create_dataframe_from_api_response_AT(api_response, target_smiles, tagged_target)
        
        # Display sample of the DataFrame
        print(f"DataFrame shape: {df.shape}")
        #print(df.head(5)[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']])
        
        # Optionally save the DataFrame to a CSV file
        #df.to_csv("retrosynthesis_results.csv", index=False)
        #print("Results saved to retrosynthesis_results.csv")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()