import pandas as pd
import json
import requests
import re
from rdkit import Chem
from config.settings import Config

def predict_retrosynthesis_EM(smiles, api_url=Config.ASKCOS_EM_URL, reaction_set="USPTO_FULL"):
    """
    Make an API call to get retrosynthesis predictions using the alternative API
    
    Parameters:
    -----------
    smiles : str or list
        The SMILES string(s) to predict retrosynthesis for
    api_url : str
        The URL of the API endpoint
    reaction_set : str
        The reaction set to use (e.g., "USPTO_FULL")
    
    Returns:
    --------
    dict
        The API response as a dictionary
    """
    # If smiles is a string, convert it to a list
    if isinstance(smiles, str):
        smiles = [smiles]
    
    # Prepare the request
    headers = {"Content-Type": "application/json"}
    data = {"smiles": smiles, "reaction_set": reaction_set}
    
    # Make the request
    response = requests.post(api_url, headers=headers, json=data)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"API request failed with status code {response.status_code}: {response.text}")

def clean_smiles(smiles):
    """
    Clean SMILES string by removing atom mapping and other annotations
    
    Parameters:
    -----------
    smiles : str
        The SMILES string to clean
    
    Returns:
    --------
    str
        The cleaned SMILES string
    """
    # Try using RDKit to canonicalize and remove atom mapping
    try:
        # Parse the SMILES string
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            # Remove atom mapping
            for atom in mol.GetAtoms():
                atom.SetAtomMapNum(0)
            # Get canonical SMILES
            canonical_smiles = Chem.MolToSmiles(mol)
            return canonical_smiles
    except:
        pass
    
    # If RDKit fails, use regex to remove common annotations
    # Remove atom mapping numbers like :1, :2, etc.
    cleaned = re.sub(r':[0-9]+', '', smiles)
    # Remove atom properties in square brackets that aren't element symbols
    cleaned = re.sub(r'\[[^]]*?([A-Z][a-z]?)[^]]*?\]', r'[\1]', cleaned)
    
    return cleaned

def convert_mapped_to_smiles(reactants):
    """
    Convert atom-mapped reactants to regular SMILES format
    
    Parameters:
    -----------
    reactants : str
        The atom-mapped reactants string
    
    Returns:
    --------
    str
        The reactants in regular SMILES format
    """
    # Split the reactants by "." if there are multiple components
    components = reactants.split(".")
    
    # Clean each component
    cleaned_components = [clean_smiles(comp) for comp in components]
    
    # Join the components back together
    return ".".join(cleaned_components)

def create_tagged_target(smiles):
    """
    Create a tagged version of the target SMILES
    This is a simplified example - you may need a more sophisticated tagging algorithm
    
    Parameters:
    -----------
    smiles : str
        The SMILES string to tag
    
    Returns:
    --------
    str
        The tagged SMILES string
    """
    # This is a simplified tagging example - replace with your actual tagging logic
    # Here we're just adding '!' to carbon and oxygen atoms in functional groups
    tagged = smiles.replace("C(=O)O", "C(=O)O!")
    tagged = tagged.replace("Cc", "C!c!")
    
    return tagged

def create_dataframe_from_api_response_EM(api_response, target_smiles, tagged_target, model_path="/home/<USER>/multiStep_retroSynthesis/assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt"):
    """
    Convert API response to a DataFrame with the specified columns.
    
    Parameters:
    -----------
    api_response : dict
        The API response as a dictionary
    target_smiles : str
        The original SMILES input
    model_path : str, optional
        The path to the model to use as T1_Model value
        
    Returns:
    --------
    pandas.DataFrame
        A DataFrame with the required columns
    """
    # Check if the API request was successful
    if api_response.get("status") != "SUCCESS":
        raise Exception(f"API returned error: {api_response.get('error', 'Unknown error')}")
    
    # Extract results from the response
    results = api_response.get("results", [])[0]
    reactants = results.get("reactants", [])
    scores = results.get("scores", [])
    reaction_ids = results.get("reaction_ids", [])
    reaction_sets = results.get("reaction_sets", [])
    
    # Create a tagged version of the target
    #tagged_target = create_tagged_target(target_smiles)
    
    # Create rows for the DataFrame
    rows = []
    for i, (reactant, score, reaction_id, reaction_set) in enumerate(zip(reactants, scores, reaction_ids, reaction_sets)):
        # Convert the reactant to regular SMILES format
        smiles_reactant = convert_mapped_to_smiles(reactant)
        
        row = {
            'T1_Model': model_path,
            'ID': f"R_1.{i+1}",
            'ID_Tag': 1,
            'ID_beam': i+1,
            'Target': target_smiles,
            'Tagged_Target': tagged_target,
            'Retro': smiles_reactant,
            'Retro_Conf': score,
            'Original_Retro': reactant,  # Keep the original format for reference
            'Reaction_ID': reaction_id,
            'Reaction_Set': reaction_set
        }
        rows.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(rows)
    
    return df

def extract_reaction_data_from_api_response(api_response):
    """
    Extract reaction data from the API response
    
    Parameters:
    -----------
    api_response : dict
        The API response as a dictionary
    
    Returns:
    --------
    pandas.DataFrame
        A DataFrame with the reaction data
    """
    # Check if the API request was successful
    if api_response.get("status") != "SUCCESS":
        raise Exception(f"API returned error: {api_response.get('error', 'Unknown error')}")
    
    # Extract results from the response
    results = api_response.get("results", [])[0]
    reaction_data = results.get("reaction_data", [])
    
    # If there's no reaction data, return an empty DataFrame
    if not reaction_data:
        return pd.DataFrame()
    
    # Create a DataFrame from the reaction data
    df_reaction = pd.DataFrame(reaction_data)
    
    return df_reaction

def predict_retrosynthesis_askscos_EM(target_smiles,tagged_target):

    # API URL
    api_url = Config.ASKCOS_EM_URL
    
    # Make API call
    try:
        print(f"Making API call to {api_url} for SMILES: {target_smiles}")
        api_response = predict_retrosynthesis_EM(target_smiles, api_url)
        
        # Create DataFrame from API response
        df = create_dataframe_from_api_response_EM(api_response, target_smiles,tagged_target)
        
        # Extract reaction data if available
        try:
            df_reaction = extract_reaction_data_from_api_response(api_response)
            if not df_reaction.empty:
                print(f"Reaction data DataFrame shape: {df_reaction.shape}")
                #df_reaction.to_csv("./output/reaction_data.csv", index=False)
                #print("Reaction data saved to reaction_data.csv")
        except Exception as e:
            print(f"Error extracting reaction data: {e}")
        
        # Display sample of the DataFrame
        #print(f"DataFrame shape: {df.shape}")
        #print(df.head(5)[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']])
        df_response = df[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']]
        return df_response
        # Optionally save the DataFrame to a CSV file
        #df.to_csv("retrosynthesis_results.csv", index=False)
        #print("Results saved to retrosynthesis_results.csv")
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    # Target SMILES
    target_smiles = "Cc1cc(Cl)cc(C(=O)O)c1N"
    tagged_target = create_tagged_target(target_smiles)
    df_response = predict_retrosynthesis_askscos_EM(target_smiles,tagged_target)
    print(df_response)