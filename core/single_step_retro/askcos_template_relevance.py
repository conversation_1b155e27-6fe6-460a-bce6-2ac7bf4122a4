import pandas as pd
import json
import requests
import re
from rdkit import Chem
from config.settings import Config

def predict_retrosynthesis_TR(smiles, api_url=Config.ASKOS_PRE_REAXYS):
    """
    Make an API call to get retrosynthesis predictions using the reaxys API
    
    Parameters:
    -----------
    smiles : str or list
        The SMILES string(s) to predict retrosynthesis for
    api_url : str
        The URL of the API endpoint
    
    Returns:
    --------
    dict
        The API response as a dictionary
    """
    # If smiles is a string, convert it to a list
    if isinstance(smiles, str):
        smiles = [smiles]
    
    # Prepare the request
    headers = {"Content-Type": "application/json"}
    data = {"smiles": smiles}
    
    # Make the request
    response = requests.post(api_url, headers=headers, json=data)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"API request failed with status code {response.status_code}: {response.text}")

def clean_smiles(smiles):
    """
    Clean SMILES string by removing atom mapping and other annotations
    
    Parameters:
    -----------
    smiles : str
        The SMILES string to clean
    
    Returns:
    --------
    str
        The cleaned SMILES string
    """
    # Try using RDKit to canonicalize and remove atom mapping
    try:
        # Parse the SMILES string
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            # Remove atom mapping
            for atom in mol.GetAtoms():
                atom.SetAtomMapNum(0)
            # Get canonical SMILES
            canonical_smiles = Chem.MolToSmiles(mol)
            return canonical_smiles
    except:
        pass
    
    # If RDKit fails, use regex to remove common annotations
    # Remove atom mapping numbers like :1, :2, etc.
    cleaned = re.sub(r':[0-9]+', '', smiles)
    # Remove atom properties in square brackets that aren't element symbols
    cleaned = re.sub(r'\[[^]]*?([A-Z][a-z]?)[^]]*?\]', r'[\1]', cleaned)
    
    return cleaned

def convert_reaction_smarts_to_reactants(reaction_smarts):
    """
    Convert reaction SMARTS to reactants in SMILES format
    
    Parameters:
    -----------
    reaction_smarts : str
        The reaction SMARTS string
    
    Returns:
    --------
    str
        The reactants in SMILES format
    """
    try:
        # Split the SMARTS by '>>' to get the reactants part
        parts = reaction_smarts.split('>>')
        if len(parts) >= 2:
            reactants = parts[1]  # The part after '>>' is the reactants in retrosynthesis
            
            # Clean the reactants SMILES
            reactants = clean_smiles(reactants)
            
            return reactants
    except:
        pass
    
    return reaction_smarts  # Return as is if parsing fails

def create_tagged_target(smiles):
    """
    Create a tagged version of the target SMILES
    This is a simplified example - you may need a more sophisticated tagging algorithm
    
    Parameters:
    -----------
    smiles : str
        The SMILES string to tag
    
    Returns:
    --------
    str
        The tagged SMILES string
    """
    # This is a simplified tagging example - replace with your actual tagging logic
    tagged = smiles
    # Tag carbons in aromatic systems
    tagged = re.sub(r'c', 'c!', tagged)
    # Tag fluorines
    tagged = re.sub(r'F', 'F!', tagged)
    # Tag nitrogen
    tagged = re.sub(r'N', 'N!', tagged)
    
    return tagged

def create_dataframe_from_api_response_TR(api_response, target_smiles, tagged_target, model_path="/home/<USER>/multiStep_retroSynthesis/assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt"):
    """
    Convert API response to a DataFrame with the specified columns.
    
    Parameters:
    -----------
    api_response : list
        The API response as a list of dictionaries
    target_smiles : str
        The original SMILES input
    model_path : str, optional
        The path to the model to use as T1_Model value
        
    Returns:
    --------
    pandas.DataFrame
        A DataFrame with the required columns
    """
    # Extract templates and reactants from the response
    templates = api_response[0].get("templates", [])
    reactants = api_response[0].get("reactants", [])
    scores = api_response[0].get("scores", [])
    
    # # Create a tagged version of the target
    # tagged_target = create_tagged_target(target_smiles)
    
    # Create rows for the DataFrame
    rows = []
    
    # In this API, we have scores per template/reactant pair
    for i, (reactant, score) in enumerate(zip(reactants, scores)):
        # Process the reactant to get clean SMILES
        clean_reactant = clean_smiles(reactant)
        
        row = {
            'T1_Model': model_path,
            'ID': f"R_1.{i+1}",
            'ID_Tag': 1,
            'ID_beam': i+1,
            'Target': target_smiles,
            'Tagged_Target': tagged_target,
            'Retro': clean_reactant,  # Store clean SMILES
            'Retro_Conf': score,
            'Original_Retro': reactant  # Keep the original format for reference
        }
        rows.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(rows)
    
    return df

def extract_template_data_from_api_response(api_response):
    """
    Extract template data from the API response
    
    Parameters:
    -----------
    api_response : list
        The API response as a list of dictionaries
    
    Returns:
    --------
    pandas.DataFrame
        A DataFrame with the template data
    """
    # Extract templates from the response
    templates = api_response[0].get("templates", [])
    
    # If there are no templates, return an empty DataFrame
    if not templates:
        return pd.DataFrame()
    
    # Create a DataFrame from the template data
    df_templates = pd.DataFrame(templates)
    
    return df_templates

def predict_retrosynthesis_askcos_TR(target_smiles,tagged_target):
    """
    Main function to run the retrosynthesis prediction and create the DataFrame
    """

    # API URL
    api_url = Config.ASKOS_PRE_REAXYS
    
    # Make API call
    try:
        print(f"Making API call to {api_url} for SMILES: {target_smiles}")
        api_response = predict_retrosynthesis_TR(target_smiles, api_url)
        
        # Create DataFrame from API response
        df = create_dataframe_from_api_response_TR(api_response, target_smiles,tagged_target)
        
        # Extract template data if available
        try:
            df_templates = extract_template_data_from_api_response(api_response)
            if not df_templates.empty:
                print(f"Template data DataFrame shape: {df_templates.shape}")
                #df_templates.to_csv("template_data.csv", index=False)
                #print("Template data saved to template_data.csv")
        except Exception as e:
            print(f"Error extracting template data: {e}")
        
        # Display sample of the DataFrame
        print(f"DataFrame shape: {df.shape}")
        df_response = df[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']]
        return df_response        
        # Optionally save the DataFrame to a CSV file
        #df.to_csv("retrosynthesis_results.csv", index=False)
        #print("Results saved to retrosynthesis_results.csv")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    target_smiles = "FC1=CC(=CC(F)=C1F)C=2C=CC=CC2N"
    tagged_target = create_tagged_target(target_smiles)
    df_response = predict_retrosynthesis_askcos_TR(target_smiles, tagged_target)
    print(df_response)    
