# Threading Improvements for Retro-Runner

## Overview
This document outlines the threading improvements made to fix the threading issues in the retro-runner application.

## Issues Identified

### 1. **Main Threading Problems in `main.py`**
- **Sequential Delays**: Threads were started with incremental 5-minute delays, defeating the purpose of parallel processing
- **Poor Error Handling**: Thread failures weren't properly tracked or reported
- **Missing Thread Coordination**: No proper synchronization between threads
- **Environment Variable Conflicts**: Global environment variables caused race conditions between threads

### 2. **Tree Search Bottleneck**
- **Sequential Tree Expansion**: The `_expand_tree` method processed nodes sequentially
- **No Parallelization**: BFS traversal was entirely single-threaded
- **Inefficient Resource Usage**: Multiple CPU cores were underutilized

## Solutions Implemented

### 1. **Fixed Main Threading Logic (`main.py`)**

#### **Before:**
```python
# Problematic code with delays
for idx, itr in enumerate(app_config.PARALLEL_CONFIGS):
    thread = Thread(target=process_single_iteration, args=(...))
    thread.start()
    if idx < len(app_config.PARALLEL_CONFIGS) - 1:
        delay = 300 * (idx + 1)  # 5 minutes delay!
        time.sleep(delay)
```

#### **After:**
```python
# All threads start simultaneously
for idx, itr in enumerate(app_config.PARALLEL_CONFIGS):
    thread = Thread(
        target=process_single_iteration,
        args=(itr, request_id, target_smiles, molecule_name, self, start_time, db_ops, idx, thread_results),
        name=f"RetroThread-{request_id}-{idx}"
    )
    threads.append(thread)

# Start all threads at once
for thread in threads:
    thread.start()
```

#### **Key Improvements:**
- ✅ **Removed delays**: All threads start simultaneously
- ✅ **Thread-safe logging**: Added locks for coordinated logging
- ✅ **Result tracking**: Shared dictionary to track thread results
- ✅ **Better error handling**: Individual thread failures don't crash others
- ✅ **Environment isolation**: Parameters passed directly instead of global env vars

### 2. **Parallel Tree Search (`core/tree/tree_search.py`)**

#### **New Parallel Tree Expansion:**
```python
def _expand_tree_parallel(self, root: MoleculeNode, kwargs: dict = {}) -> None:
    """
    Expand the search tree using parallel BFS.
    Nodes at the same depth level are processed in parallel.
    """
    max_workers = kwargs.get('max_workers', 4)
    current_level = [root]
    
    while current_level:
        next_level = []
        
        # Process all nodes at current level in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_node = {
                executor.submit(self._process_single_node_parallel, node, beam_width, iteration_count): node
                for node in current_level
            }
            
            for future in as_completed(future_to_node):
                new_nodes = future.result()
                next_level.extend(new_nodes)
        
        current_level = next_level
```

#### **Key Features:**
- ✅ **Level-wise parallelization**: Nodes at same depth processed in parallel
- ✅ **Thread-safe operations**: All shared operations use locks
- ✅ **Configurable workers**: Adjustable number of parallel workers
- ✅ **Maintains BFS semantics**: Preserves correct search order
- ✅ **Error isolation**: Individual node failures don't crash the search

### 3. **Thread-Safe Node Operations**

#### **Parallel Node Expansion:**
```python
def _expand_molecule_node_parallel(self, mol_node: MoleculeNode) -> None:
    """Thread-safe version of _expand_molecule_node"""
    # Thread-safe terminal checking
    if self.terminal_checker.is_terminal(mol_node):
        with tree_expansion_lock:
            print(f"[PARALLEL] Hit terminal node: {mol_node.smiles}")
        mol_node.status = NodeStatus.TERMINAL
        return
    
    # Thread-safe disconnection processing
    try:
        disconnections_df = self.single_step_api.get_retrosynthesis_reactions(mol_node.smiles)
        # ... process disconnections safely
    except Exception as e:
        with tree_expansion_lock:
            logger.error(f"[PARALLEL] Error expanding molecule {mol_node.smiles}: {e}")
```

## Performance Benefits

### 1. **Main Thread Parallelization**
- **Before**: 6 configurations × 5 minutes delay = 25+ minutes startup time
- **After**: All configurations start immediately = ~0 seconds startup time
- **Improvement**: ~25 minutes saved per task

### 2. **Tree Search Parallelization**
- **Before**: Single-threaded BFS traversal
- **After**: Multi-threaded level-wise expansion
- **Expected Improvement**: 2-4x speedup depending on tree width and CPU cores

### 3. **Resource Utilization**
- **Before**: Single CPU core usage during tree search
- **After**: Multi-core utilization for both main threads and tree expansion
- **Improvement**: Better CPU utilization, faster overall processing

## Configuration Options

### **Enable Parallel Tree Search:**
```python
kwargs = {
    "use_parallel": True,      # Enable parallel tree expansion
    "max_workers": 2,          # Number of workers for tree expansion
    "max_depth": 3,
    "beam_width": 10
}
```

### **Thread Configuration:**
- `max_workers`: Number of parallel workers for tree expansion (default: 2)
- `use_parallel`: Enable/disable parallel tree expansion (default: True)

## Testing

### **Test Scripts Created:**
1. `test_threading.py` - Tests main thread coordination
2. `test_parallel_tree.py` - Tests parallel tree expansion

### **Run Tests:**
```bash
python test_threading.py
python test_parallel_tree.py
```

## Monitoring and Debugging

### **Enhanced Logging:**
- `[THREAD-X]` - Main thread operations
- `[PARALLEL]` - Parallel tree expansion operations
- Thread-safe logging with locks to prevent garbled output

### **Performance Metrics:**
- Thread completion times
- Route counts per thread
- Success/failure ratios
- Processing time comparisons

## Backward Compatibility

- ✅ **Sequential mode preserved**: Set `use_parallel=False` to use original behavior
- ✅ **Configuration compatible**: All existing configurations work unchanged
- ✅ **API unchanged**: No breaking changes to external interfaces

## Next Steps

1. **Monitor Performance**: Track actual performance improvements in production
2. **Tune Parameters**: Adjust `max_workers` based on system resources
3. **Add Metrics**: Implement detailed performance monitoring
4. **Optimize Further**: Consider additional parallelization opportunities

## Summary

The threading improvements address the core issues:
- ✅ **Fixed thread startup delays** - All threads start immediately
- ✅ **Added parallel tree search** - Multi-threaded BFS traversal
- ✅ **Improved error handling** - Better thread coordination and error isolation
- ✅ **Enhanced monitoring** - Better logging and result tracking
- ✅ **Maintained compatibility** - No breaking changes

These changes should significantly improve the performance and reliability of the retro-runner's parallel processing capabilities.
